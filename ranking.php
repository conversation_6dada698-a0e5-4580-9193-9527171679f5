<?php
require_once 'config/config.php';
require_once 'includes/topsis.php';

$pageTitle = 'Ranking TOPSIS';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'calculate_topsis':
                    $alternatif = fetchAll("SELECT * FROM alternatif ORDER BY kode_alternatif");
                    $kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");
                    
                    $result = TOPSIS::process($alternatif, $kriteria);
                    
                    setAlert('success', 'Perhitungan TOPSIS berhasil dilakukan. Ranking telah diperbarui.');
                    break;
                    
                case 'clear_results':
                    executeUpdate("DELETE FROM hasil_topsis");
                    setAlert('success', 'Hasil ranking berhasil dihapus');
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('ranking.php');
}

// Ambil data
$alternatif = fetchAll("SELECT * FROM alternatif ORDER BY kode_alternatif");
$kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");

// Validasi data
$validationErrors = TOPSIS::validateData($alternatif, $kriteria);

// Ambil hasil ranking terbaru
$ranking = fetchAll("
    SELECT ht.*, a.kode_alternatif, a.nama_alternatif, a.deskripsi
    FROM hasil_topsis ht
    JOIN alternatif a ON ht.alternatif_id = a.id
    ORDER BY ht.ranking ASC
");

// Ambil detail perhitungan jika ada
$calculationDetails = null;
if (!empty($ranking) && empty($validationErrors)) {
    try {
        $calculationDetails = TOPSIS::process($alternatif, $kriteria);
    } catch (Exception $e) {
        // Ignore error untuk display
    }
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Status dan Kontrol -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>Status Perhitungan TOPSIS
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($validationErrors)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Data belum lengkap untuk perhitungan TOPSIS:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach ($validationErrors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <div class="mt-3">
                            <a href="kriteria.php" class="btn btn-sm btn-primary me-2">
                                <i class="fas fa-list-alt me-1"></i>Kelola Kriteria
                            </a>
                            <a href="alternatif.php" class="btn btn-sm btn-success me-2">
                                <i class="fas fa-leaf me-1"></i>Kelola Alternatif
                            </a>
                            <a href="perbandingan.php" class="btn btn-sm btn-warning me-2">
                                <i class="fas fa-balance-scale me-1"></i>Hitung Bobot AHP
                            </a>
                            <a href="nilai_alternatif.php" class="btn btn-sm btn-info">
                                <i class="fas fa-calculator me-1"></i>Input Nilai
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo count($alternatif); ?></h4>
                                <small class="text-muted">Alternatif</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success"><?php echo count($kriteria); ?></h4>
                                <small class="text-muted">Kriteria</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo count($ranking); ?></h4>
                                <small class="text-muted">Hasil Ranking</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php if (!empty($ranking)): ?>
                                    <h4 class="text-success">
                                        <i class="fas fa-check-circle"></i>
                                    </h4>
                                    <small class="text-muted">Siap</small>
                                <?php else: ?>
                                    <h4 class="text-warning">
                                        <i class="fas fa-clock"></i>
                                    </h4>
                                    <small class="text-muted">Belum Dihitung</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-success me-2" onclick="calculateTOPSIS()">
                            <i class="fas fa-calculator me-2"></i>Hitung Ranking TOPSIS
                        </button>
                        
                        <?php if (!empty($ranking)): ?>
                            <button type="button" class="btn btn-danger" onclick="clearResults()">
                                <i class="fas fa-trash me-2"></i>Hapus Hasil
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Hasil Ranking -->
    <?php if (!empty($ranking)): ?>
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-medal me-2"></i>Hasil Ranking
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($ranking as $item): ?>
                    <div class="ranking-item">
                        <div class="d-flex align-items-center">
                            <div class="ranking-badge me-3">
                                <?php echo $item['ranking']; ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <?php echo $item['nama_alternatif']; ?>
                                    <span class="badge bg-secondary ms-2"><?php echo $item['kode_alternatif']; ?></span>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <?php echo $item['deskripsi']; ?>
                                </p>
                                <small class="text-muted">
                                    Skor Preferensi: <strong><?php echo formatNumber($item['skor_preferensi']); ?></strong>
                                </small>
                            </div>
                            <div class="text-end">
                                <?php if ($item['ranking'] == 1): ?>
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-crown me-1"></i>Terbaik
                                    </span>
                                <?php elseif ($item['ranking'] == 2): ?>
                                    <span class="badge bg-info fs-6">
                                        <i class="fas fa-medal me-1"></i>Kedua
                                    </span>
                                <?php elseif ($item['ranking'] == 3): ?>
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-award me-1"></i>Ketiga
                                    </span>
                                <?php endif; ?>
                                
                                <div class="mt-2">
                                    <div class="progress" style="width: 150px; height: 8px;">
                                        <div class="progress-bar bg-primary" role="progressbar" 
                                             style="width: <?php echo ($item['skor_preferensi'] * 100); ?>%">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Ranking berdasarkan skor preferensi TOPSIS (semakin tinggi semakin baik)
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Grafik Ranking -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Grafik Skor
                </h5>
            </div>
            <div class="card-body">
                <canvas id="rankingChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Detail Perhitungan -->
<?php if ($calculationDetails && !empty($ranking)): ?>
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>Detail Perhitungan TOPSIS
                </h5>
            </div>
            <div class="card-body">
                <!-- Tabs -->
                <ul class="nav nav-tabs" id="calculationTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="decision-tab" data-bs-toggle="tab"
                                data-bs-target="#decision" type="button" role="tab">
                            Matriks Keputusan
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="normalized-tab" data-bs-toggle="tab"
                                data-bs-target="#normalized" type="button" role="tab">
                            Matriks Ternormalisasi
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="weighted-tab" data-bs-toggle="tab"
                                data-bs-target="#weighted" type="button" role="tab">
                            Matriks Terbobot
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ideal-tab" data-bs-toggle="tab"
                                data-bs-target="#ideal" type="button" role="tab">
                            Solusi Ideal
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="calculationTabsContent">
                    <!-- Matriks Keputusan -->
                    <div class="tab-pane fade show active" id="decision" role="tabpanel">
                        <h6>Matriks Keputusan Awal</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Alternatif</th>
                                        <?php foreach ($kriteria as $k): ?>
                                            <th class="text-center"><?php echo $k['kode_kriteria']; ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $formattedDecision = TOPSIS::formatMatrixForDisplay($calculationDetails['decisionMatrix'], 2);
                                    foreach ($alternatif as $i => $alt):
                                    ?>
                                        <tr>
                                            <td><?php echo $alt['kode_alternatif']; ?></td>
                                            <?php foreach ($kriteria as $j => $k): ?>
                                                <td class="text-center"><?php echo $formattedDecision[$i][$j]; ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Matriks Ternormalisasi -->
                    <div class="tab-pane fade" id="normalized" role="tabpanel">
                        <h6>Matriks Ternormalisasi</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Alternatif</th>
                                        <?php foreach ($kriteria as $k): ?>
                                            <th class="text-center"><?php echo $k['kode_kriteria']; ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $formattedNormalized = TOPSIS::formatMatrixForDisplay($calculationDetails['normalizedMatrix']);
                                    foreach ($alternatif as $i => $alt):
                                    ?>
                                        <tr>
                                            <td><?php echo $alt['kode_alternatif']; ?></td>
                                            <?php foreach ($kriteria as $j => $k): ?>
                                                <td class="text-center"><?php echo $formattedNormalized[$i][$j]; ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Matriks Terbobot -->
                    <div class="tab-pane fade" id="weighted" role="tabpanel">
                        <h6>Matriks Ternormalisasi Terbobot</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Alternatif</th>
                                        <?php foreach ($kriteria as $k): ?>
                                            <th class="text-center">
                                                <?php echo $k['kode_kriteria']; ?><br>
                                                <small>(w=<?php echo formatNumber($k['bobot'], 3); ?>)</small>
                                            </th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $formattedWeighted = TOPSIS::formatMatrixForDisplay($calculationDetails['weightedMatrix']);
                                    foreach ($alternatif as $i => $alt):
                                    ?>
                                        <tr>
                                            <td><?php echo $alt['kode_alternatif']; ?></td>
                                            <?php foreach ($kriteria as $j => $k): ?>
                                                <td class="text-center"><?php echo $formattedWeighted[$i][$j]; ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Solusi Ideal -->
                    <div class="tab-pane fade" id="ideal" role="tabpanel">
                        <h6>Solusi Ideal Positif dan Negatif</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">Solusi Ideal Positif (A+)</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Kriteria</th>
                                                <th>Nilai</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($kriteria as $j => $k): ?>
                                                <tr>
                                                    <td><?php echo $k['kode_kriteria']; ?></td>
                                                    <td class="text-center">
                                                        <?php echo formatNumber($calculationDetails['idealSolutions']['positive'][$j]); ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">Solusi Ideal Negatif (A-)</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Kriteria</th>
                                                <th>Nilai</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($kriteria as $j => $k): ?>
                                                <tr>
                                                    <td><?php echo $k['kode_kriteria']; ?></td>
                                                    <td class="text-center">
                                                        <?php echo formatNumber($calculationDetails['idealSolutions']['negative'][$j]); ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <h6 class="mt-4">Jarak dan Skor Preferensi</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Alternatif</th>
                                        <th>D+ (Jarak ke A+)</th>
                                        <th>D- (Jarak ke A-)</th>
                                        <th>Skor Preferensi</th>
                                        <th>Ranking</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($calculationDetails['ranking'] as $item): ?>
                                        <tr>
                                            <td><?php echo $item['alternatif']['kode_alternatif']; ?></td>
                                            <td class="text-center">
                                                <?php
                                                $altIndex = array_search($item['alternatif']['id'], array_column($alternatif, 'id'));
                                                echo formatNumber($calculationDetails['distances']['positive'][$altIndex]);
                                                ?>
                                            </td>
                                            <td class="text-center">
                                                <?php echo formatNumber($calculationDetails['distances']['negative'][$altIndex]); ?>
                                            </td>
                                            <td class="text-center">
                                                <strong><?php echo formatNumber($item['skor']); ?></strong>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-primary"><?php echo $item['ranking']; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$pageScripts = "
<script>
function calculateTOPSIS() {
    if (confirm('Yakin ingin menghitung ranking TOPSIS? Hasil sebelumnya akan ditimpa.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"calculate_topsis\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function clearResults() {
    if (confirm('Yakin ingin menghapus semua hasil ranking?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"clear_results\">';
        document.body.appendChild(form);
        form.submit();
    }
}

// Chart untuk ranking
" . (!empty($ranking) ? "
const rankingCtx = document.getElementById('rankingChart').getContext('2d');
const rankingChart = new Chart(rankingCtx, {
    type: 'bar',
    data: {
        labels: " . json_encode(array_column($ranking, 'kode_alternatif')) . ",
        datasets: [{
            label: 'Skor Preferensi',
            data: " . json_encode(array_column($ranking, 'skor_preferensi')) . ",
            backgroundColor: [
                '#FFD700', // Gold untuk ranking 1
                '#C0C0C0', // Silver untuk ranking 2
                '#CD7F32', // Bronze untuk ranking 3
                '#2E8B57', // Green untuk lainnya
                '#2E8B57',
                '#2E8B57'
            ],
            borderColor: '#fff',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 1
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
" : "") . "
</script>
";

include 'includes/footer.php';
?>
