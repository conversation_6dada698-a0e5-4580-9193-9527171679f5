/* Custom CSS untuk SPK Pupuk Organik */

:root {
    --primary-color: #2E8B57;
    --secondary-color: #90EE90;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%) !important;
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.login-form-container {
    max-width: 450px;
    width: 100%;
}

.login-form-container .card {
    border-radius: 15px;
    overflow: hidden;
}

.login-form-container .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.login-form-container .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

.login-form-container .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.login-form-container .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, #1e5f3f 100%);
    min-height: 100vh;
    box-shadow: var(--shadow-lg);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    margin: 5px 10px;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* Header Styles */
.main-header {
    background: white;
    box-shadow: var(--shadow);
    border-bottom: 3px solid var(--primary-color);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Button Styles */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 8px 20px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(46, 139, 87, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #6f42c1 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e83e8c 100%);
    border: none;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(46, 139, 87, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Dashboard Stats Cards */
.stats-card {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.stats-card .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
}

/* Matrix Table Styles */
.matrix-table {
    font-size: 0.9rem;
}

.matrix-table th,
.matrix-table td {
    text-align: center;
    padding: 8px;
    min-width: 80px;
}

.matrix-table .diagonal {
    background-color: rgba(46, 139, 87, 0.1);
    font-weight: bold;
}

/* Ranking Styles */
.ranking-item {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.ranking-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow);
}

.ranking-badge {
    background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .login-form-container {
        padding: 20px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
