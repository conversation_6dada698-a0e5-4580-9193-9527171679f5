<?php
require_once 'config/config.php';
require_once 'includes/ahp.php';

$pageTitle = 'Perbandingan Kriteria (AHP)';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'save_comparison':
                    $kriteria1_id = (int)$_POST['kriteria1_id'];
                    $kriteria2_id = (int)$_POST['kriteria2_id'];
                    $nilai = (float)$_POST['nilai_perbandingan'];
                    
                    if ($kriteria1_id == $kriteria2_id) {
                        throw new Exception('Tidak dapat membandingkan kriteria dengan dirinya sendiri');
                    }
                    
                    if ($nilai < 1/9 || $nilai > 9) {
                        throw new Exception('Nilai perbandingan harus antara 1/9 dan 9');
                    }
                    
                    // Hapus perbandingan yang sudah ada (jika ada)
                    executeUpdate("DELETE FROM perbandingan_kriteria WHERE 
                                 (kriteria1_id = ? AND kriteria2_id = ?) OR 
                                 (kriteria1_id = ? AND kriteria2_id = ?)", 
                                [$kriteria1_id, $kriteria2_id, $kriteria2_id, $kriteria1_id]);
                    
                    // Simpan perbandingan baru
                    $query = "INSERT INTO perbandingan_kriteria (kriteria1_id, kriteria2_id, nilai_perbandingan) VALUES (?, ?, ?)";
                    insertData($query, [$kriteria1_id, $kriteria2_id, $nilai]);
                    
                    setAlert('success', 'Perbandingan berhasil disimpan');
                    break;
                    
                case 'calculate_weights':
                    $kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");
                    
                    if (count($kriteria) < 2) {
                        throw new Exception('Minimal harus ada 2 kriteria untuk menghitung bobot');
                    }
                    
                    // Validasi kelengkapan matriks
                    $validation = AHP::validateMatrix($kriteria);
                    if (!$validation['complete']) {
                        throw new Exception("Matriks perbandingan belum lengkap. Masih kurang {$validation['missing']} perbandingan.");
                    }
                    
                    // Buat matriks perbandingan
                    $matrix = AHP::createPairwiseMatrix($kriteria);
                    
                    // Hitung bobot
                    $weights = AHP::calculateWeights($matrix);
                    
                    // Hitung consistency ratio
                    $cr = AHP::calculateConsistencyRatio($matrix, $weights);
                    
                    if ($cr > 0.1) {
                        setAlert('warning', "Consistency Ratio = " . number_format($cr, 4) . " > 0.1. Matriks tidak konsisten, sebaiknya periksa kembali perbandingan.");
                    }
                    
                    // Simpan bobot
                    AHP::saveWeights($kriteria, $weights);
                    
                    setAlert('success', "Bobot berhasil dihitung. Consistency Ratio = " . number_format($cr, 4));
                    break;
                    
                case 'reset_comparisons':
                    AHP::resetComparisons();
                    setAlert('success', 'Semua perbandingan berhasil direset');
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('perbandingan.php');
}

// Ambil data
$kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");
$validation = AHP::validateMatrix($kriteria);
$missingComparisons = AHP::generateMissingComparisons($kriteria);

// Jika ada kriteria, buat matriks
$matrix = null;
$weights = null;
$cr = null;

if (count($kriteria) >= 2) {
    $matrix = AHP::createPairwiseMatrix($kriteria);
    if ($validation['complete']) {
        $weights = AHP::calculateWeights($matrix);
        $cr = AHP::calculateConsistencyRatio($matrix, $weights);
    }
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Status dan Kontrol -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2"></i>Status Perbandingan Kriteria
                </h5>
            </div>
            <div class="card-body">
                <?php if (count($kriteria) < 2): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Minimal harus ada 2 kriteria untuk melakukan perbandingan.
                        <a href="kriteria.php" class="btn btn-sm btn-primary ms-2">
                            <i class="fas fa-plus me-1"></i>Tambah Kriteria
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo count($kriteria); ?></h4>
                                <small class="text-muted">Total Kriteria</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo $validation['existing']; ?>/<?php echo $validation['required']; ?></h4>
                                <small class="text-muted">Perbandingan Selesai</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="<?php echo $validation['complete'] ? 'text-success' : 'text-warning'; ?>">
                                    <?php echo $validation['complete'] ? '100%' : number_format(($validation['existing'] / $validation['required']) * 100, 1) . '%'; ?>
                                </h4>
                                <small class="text-muted">Progress</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php if ($cr !== null): ?>
                                    <h4 class="<?php echo $cr <= 0.1 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo number_format($cr, 4); ?>
                                    </h4>
                                    <small class="text-muted">Consistency Ratio</small>
                                <?php else: ?>
                                    <h4 class="text-muted">-</h4>
                                    <small class="text-muted">Belum Dihitung</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <?php if ($validation['complete']): ?>
                            <button type="button" class="btn btn-success me-2" onclick="calculateWeights()">
                                <i class="fas fa-calculator me-2"></i>Hitung Bobot
                            </button>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-danger" onclick="resetComparisons()">
                            <i class="fas fa-redo me-2"></i>Reset Semua
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Form Input Perbandingan -->
    <?php if (!empty($missingComparisons)): ?>
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Input Perbandingan
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="comparisonForm">
                    <input type="hidden" name="action" value="save_comparison">
                    
                    <div class="mb-3">
                        <label class="form-label">Pilih Kriteria untuk Dibandingkan</label>
                        <select class="form-select" id="comparisonSelect" onchange="loadComparison()">
                            <option value="">Pilih perbandingan...</option>
                            <?php foreach ($missingComparisons as $index => $comp): ?>
                                <option value="<?php echo $index; ?>" 
                                        data-k1-id="<?php echo $comp['kriteria1']['id']; ?>"
                                        data-k1-name="<?php echo $comp['kriteria1']['nama_kriteria']; ?>"
                                        data-k2-id="<?php echo $comp['kriteria2']['id']; ?>"
                                        data-k2-name="<?php echo $comp['kriteria2']['nama_kriteria']; ?>">
                                    <?php echo $comp['kriteria1']['nama_kriteria']; ?> vs <?php echo $comp['kriteria2']['nama_kriteria']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div id="comparisonInputs" style="display: none;">
                        <input type="hidden" name="kriteria1_id" id="kriteria1_id">
                        <input type="hidden" name="kriteria2_id" id="kriteria2_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Perbandingan</label>
                            <div class="text-center mb-3">
                                <strong id="kriteria1_name"></strong>
                                <span class="mx-3">vs</span>
                                <strong id="kriteria2_name"></strong>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="nilai_perbandingan" class="form-label">Nilai Perbandingan</label>
                            <select class="form-select" name="nilai_perbandingan" id="nilai_perbandingan" required>
                                <option value="">Pilih nilai...</option>
                                <?php foreach (AHP::$skala as $nilai => $deskripsi): ?>
                                    <option value="<?php echo $nilai; ?>"><?php echo $nilai; ?> - <?php echo $deskripsi; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                Pilih seberapa penting kriteria pertama dibanding kriteria kedua
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Perbandingan
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Matriks Perbandingan -->
    <?php if ($matrix): ?>
    <div class="col-lg-<?php echo !empty($missingComparisons) ? '6' : '12'; ?> mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>Matriks Perbandingan
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered matrix-table">
                        <thead>
                            <tr>
                                <th></th>
                                <?php foreach ($kriteria as $k): ?>
                                    <th class="text-center"><?php echo $k['kode_kriteria']; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $formattedMatrix = AHP::formatMatrixForDisplay($matrix, $kriteria);
                            foreach ($kriteria as $i => $k): 
                            ?>
                                <tr>
                                    <th><?php echo $k['kode_kriteria']; ?></th>
                                    <?php foreach ($kriteria as $j => $k2): ?>
                                        <td class="text-center <?php echo $i == $j ? 'diagonal' : ''; ?>">
                                            <?php echo $formattedMatrix[$i][$j]; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Hasil Bobot -->
<?php if ($weights): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-weight me-2"></i>Hasil Perhitungan Bobot
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Kriteria</th>
                                        <th>Bobot</th>
                                        <th>Persentase</th>
                                        <th>Prioritas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $weightData = [];
                                    for ($i = 0; $i < count($kriteria); $i++) {
                                        $weightData[] = [
                                            'kriteria' => $kriteria[$i],
                                            'bobot' => $weights[$i]
                                        ];
                                    }

                                    // Sort by weight descending
                                    usort($weightData, function($a, $b) {
                                        return $b['bobot'] <=> $a['bobot'];
                                    });

                                    foreach ($weightData as $index => $item):
                                    ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $item['kriteria']['nama_kriteria']; ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo $item['kriteria']['kode_kriteria']; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary fs-6">
                                                    <?php echo formatNumber($item['bobot']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: <?php echo ($item['bobot'] * 100); ?>%">
                                                        <?php echo number_format($item['bobot'] * 100, 1); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $index == 0 ? 'success' : ($index == 1 ? 'info' : 'secondary'); ?>">
                                                    #<?php echo $index + 1; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="text-center">
                            <h6>Consistency Ratio</h6>
                            <div class="mb-3">
                                <span class="badge bg-<?php echo $cr <= 0.1 ? 'success' : 'danger'; ?> fs-4">
                                    <?php echo number_format($cr, 4); ?>
                                </span>
                            </div>
                            <p class="text-muted">
                                <?php if ($cr <= 0.1): ?>
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    Matriks konsisten
                                <?php else: ?>
                                    <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                                    Matriks tidak konsisten
                                <?php endif; ?>
                            </p>
                            <small class="text-muted">
                                CR ≤ 0.1 menunjukkan konsistensi yang dapat diterima
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$pageScripts = "
<script>
function loadComparison() {
    const select = document.getElementById('comparisonSelect');
    const inputs = document.getElementById('comparisonInputs');

    if (select.value === '') {
        inputs.style.display = 'none';
        return;
    }

    const option = select.options[select.selectedIndex];

    document.getElementById('kriteria1_id').value = option.dataset.k1Id;
    document.getElementById('kriteria2_id').value = option.dataset.k2Id;
    document.getElementById('kriteria1_name').textContent = option.dataset.k1Name;
    document.getElementById('kriteria2_name').textContent = option.dataset.k2Name;

    inputs.style.display = 'block';
}

function calculateWeights() {
    if (confirm('Yakin ingin menghitung bobot kriteria? Bobot yang ada akan diperbarui.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"calculate_weights\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function resetComparisons() {
    if (confirm('Yakin ingin menghapus semua perbandingan? Tindakan ini tidak dapat dibatalkan.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"reset_comparisons\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
