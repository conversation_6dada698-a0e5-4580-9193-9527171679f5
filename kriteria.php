<?php
require_once 'config/config.php';

$pageTitle = 'Manajemen Kriteria';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $kode = sanitize($_POST['kode_kriteria']);
                    $nama = sanitize($_POST['nama_kriteria']);
                    $deskripsi = sanitize($_POST['deskripsi']);
                    $jenis = sanitize($_POST['jenis']);
                    
                    // Validasi
                    if (empty($kode) || empty($nama)) {
                        throw new Exception('Kode dan nama kriteria harus diisi');
                    }
                    
                    // Cek duplikasi kode
                    $existing = fetchOne("SELECT id FROM kriteria WHERE kode_kriteria = ?", [$kode]);
                    if ($existing) {
                        throw new Exception('Kode kriteria sudah ada');
                    }
                    
                    $query = "INSERT INTO kriteria (kode_kriteria, nama_kriteria, deskripsi, jenis) VALUES (?, ?, ?, ?)";
                    insertData($query, [$kode, $nama, $deskripsi, $jenis]);
                    
                    setAlert('success', 'Kriteria berhasil ditambahkan');
                    break;
                    
                case 'edit':
                    $id = (int)$_POST['id'];
                    $kode = sanitize($_POST['kode_kriteria']);
                    $nama = sanitize($_POST['nama_kriteria']);
                    $deskripsi = sanitize($_POST['deskripsi']);
                    $jenis = sanitize($_POST['jenis']);
                    
                    if (empty($kode) || empty($nama)) {
                        throw new Exception('Kode dan nama kriteria harus diisi');
                    }
                    
                    // Cek duplikasi kode (kecuali untuk record yang sedang diedit)
                    $existing = fetchOne("SELECT id FROM kriteria WHERE kode_kriteria = ? AND id != ?", [$kode, $id]);
                    if ($existing) {
                        throw new Exception('Kode kriteria sudah ada');
                    }
                    
                    $query = "UPDATE kriteria SET kode_kriteria = ?, nama_kriteria = ?, deskripsi = ?, jenis = ? WHERE id = ?";
                    executeUpdate($query, [$kode, $nama, $deskripsi, $jenis, $id]);
                    
                    setAlert('success', 'Kriteria berhasil diupdate');
                    break;
                    
                case 'delete':
                    $id = (int)$_POST['id'];
                    
                    // Cek apakah kriteria digunakan dalam perbandingan
                    $used = fetchOne("SELECT COUNT(*) as total FROM perbandingan_kriteria WHERE kriteria1_id = ? OR kriteria2_id = ?", [$id, $id]);
                    if ($used['total'] > 0) {
                        throw new Exception('Kriteria tidak dapat dihapus karena sudah digunakan dalam perbandingan');
                    }
                    
                    executeUpdate("DELETE FROM kriteria WHERE id = ?", [$id]);
                    setAlert('success', 'Kriteria berhasil dihapus');
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('kriteria.php');
}

// Ambil data kriteria
$kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>Data Kriteria
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                    <i class="fas fa-plus me-2"></i>Tambah Kriteria
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Kode</th>
                                <th>Nama Kriteria</th>
                                <th>Jenis</th>
                                <th>Bobot</th>
                                <th>Deskripsi</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($kriteria)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada data kriteria</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($kriteria as $index => $item): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $item['kode_kriteria']; ?></span>
                                        </td>
                                        <td><?php echo $item['nama_kriteria']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $item['jenis'] == 'benefit' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($item['jenis']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatNumber($item['bobot']); ?></td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo strlen($item['deskripsi']) > 50 ? substr($item['deskripsi'], 0, 50) . '...' : $item['deskripsi']; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-info" 
                                                        onclick="editKriteria(<?php echo htmlspecialchars(json_encode($item)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteKriteria(<?php echo $item['id']; ?>, '<?php echo $item['nama_kriteria']; ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Tambah Kriteria
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="kode_kriteria" class="form-label">Kode Kriteria</label>
                        <input type="text" class="form-control" id="kode_kriteria" name="kode_kriteria" 
                               placeholder="Contoh: C1" required maxlength="10">
                    </div>
                    
                    <div class="mb-3">
                        <label for="nama_kriteria" class="form-label">Nama Kriteria</label>
                        <input type="text" class="form-control" id="nama_kriteria" name="nama_kriteria" 
                               placeholder="Masukkan nama kriteria" required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="jenis" class="form-label">Jenis Kriteria</label>
                        <select class="form-select" id="jenis" name="jenis" required>
                            <option value="">Pilih jenis kriteria</option>
                            <option value="benefit">Benefit (Semakin besar semakin baik)</option>
                            <option value="cost">Cost (Semakin kecil semakin baik)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="deskripsi" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3" 
                                  placeholder="Deskripsi kriteria (opsional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Kriteria
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_kode_kriteria" class="form-label">Kode Kriteria</label>
                        <input type="text" class="form-control" id="edit_kode_kriteria" name="kode_kriteria" 
                               required maxlength="10">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_nama_kriteria" class="form-label">Nama Kriteria</label>
                        <input type="text" class="form-control" id="edit_nama_kriteria" name="nama_kriteria" 
                               required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_jenis" class="form-label">Jenis Kriteria</label>
                        <select class="form-select" id="edit_jenis" name="jenis" required>
                            <option value="benefit">Benefit (Semakin besar semakin baik)</option>
                            <option value="cost">Cost (Semakin kecil semakin baik)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_deskripsi" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="edit_deskripsi" name="deskripsi" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$pageScripts = "
<script>
function editKriteria(data) {
    document.getElementById('edit_id').value = data.id;
    document.getElementById('edit_kode_kriteria').value = data.kode_kriteria;
    document.getElementById('edit_nama_kriteria').value = data.nama_kriteria;
    document.getElementById('edit_jenis').value = data.jenis;
    document.getElementById('edit_deskripsi').value = data.deskripsi || '';
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteKriteria(id, nama) {
    if (confirm('Yakin ingin menghapus kriteria \"' + nama + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
