# Sistem Pendukung Keputusan Pemilihan Pupuk Organik

Sistem Pendukung Keputusan (SPK) untuk pemilihan komposisi pupuk organik pada rumput gajah mini berbasis web di Aming Flora menggunakan metode AHP (Analytical Hierarchy Process) untuk pembobotan kriteria dan TOPSIS (Technique for Order Preference by Similarity to Ideal Solution) untuk perangkingan alternatif.

## Fitur Utama

### 1. Sistem Autentikasi

- Login/Logout dengan session management
- Role-based access control (Admin & User)
- Manajemen profil pengguna
- Reset password

### 2. Manajemen Data Master

- **Kriteria**: Kelola kriteria penilaian pupuk organik
- **Alternatif**: <PERSON><PERSON><PERSON> jenis-jenis pupuk organik
- **Nilai Alternatif**: Input nilai setiap alternatif untuk setiap kriteria
- **Users**: <PERSON><PERSON><PERSON><PERSON> pengguna (khusus admin)

### 3. Metode AHP (Analytical Hierarchy Process)

- Perbandingan berpasangan antar kriteria
- Perhitungan bobot kriteria otomatis
- Validasi konsistensi matriks (Consistency Ratio)
- Visualisasi matriks perbandingan

### 4. Metode TOPSIS

- Normalisasi matriks keputusan
- Perhitungan solusi ideal positif dan negatif
- Ranking alternatif berdasarkan skor preferensi
- Detail perhitungan lengkap

### 5. Dashboard & Visualisasi

- Dashboard dengan statistik lengkap
- Grafik bobot kriteria dan nilai alternatif
- Ranking terbaru dengan visualisasi
- Interface yang user-friendly dan responsive

## Kriteria Penilaian

1. **Kebutuhan Nutrisi (C1)** - Kemampuan pupuk memenuhi kebutuhan nutrisi tanaman
2. **Kondisi Tanah (C2)** - Kesesuaian pupuk dengan kondisi tanah
3. **Daya Serap (C3)** - Kemampuan tanaman menyerap nutrisi dari pupuk
4. **Biaya (C4)** - Biaya pengadaan pupuk (kriteria cost)
5. **Ketersediaan (C5)** - Kemudahan mendapatkan pupuk di pasaran
6. **Dampak Lingkungan (C6)** - Dampak positif pupuk terhadap lingkungan

## Alternatif Pupuk Organik

1. **Kompos Bokashi (A1)** - Pupuk organik hasil fermentasi dengan mikroorganisme efektif
2. **Pupuk Kandang (A2)** - Pupuk organik dari kotoran hewan ternak yang telah difermentasi
3. **Pupuk Organik Cair (A3)** - Pupuk organik dalam bentuk cair yang mudah diserap tanaman
4. **Pupuk Organik Padat Komersial (A4)** - Pupuk organik padat yang diproduksi secara komersial

## Persyaratan Sistem

### Server Requirements

- PHP 7.4 atau lebih baru
- MySQL 5.7 atau MariaDB 10.2
- Apache/Nginx web server
- PDO MySQL extension

### Browser Requirements

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Instalasi

### 1. Persiapan Database

1. Buat database MySQL baru:

   ```sql
   CREATE DATABASE spk_amingflora;
   ```

2. Import struktur database:
   ```bash
   mysql -u username -p spk_amingflora < database.sql
   ```

### 2. Konfigurasi Aplikasi

1. Edit file `config/database.php` sesuai dengan konfigurasi database Anda:

   ```php
   private $host = 'localhost';
   private $db_name = 'spk_amingflora';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

2. Pastikan folder `logs/` dapat ditulis oleh web server:
   ```bash
   chmod 755 logs/
   ```

### 3. Deployment

1. Upload semua file ke web server
2. Pastikan document root mengarah ke folder aplikasi
3. Akses aplikasi melalui browser

## Penggunaan

### 1. Login Pertama Kali

- Username: `admin`
- Password: `password`

### 2. Langkah-langkah Penggunaan

#### A. Setup Data Master

1. **Kelola Kriteria**

   - Masuk ke menu "Kriteria"
   - Tambah/edit kriteria sesuai kebutuhan
   - Pastikan jenis kriteria (benefit/cost) sudah benar

2. **Kelola Alternatif**

   - Masuk ke menu "Alternatif"
   - Tambah jenis-jenis pupuk organik yang akan dibandingkan

3. **Input Nilai Alternatif**
   - Masuk ke menu "Nilai Alternatif"
   - Isi nilai setiap alternatif untuk setiap kriteria (skala 0-10)

#### B. Perhitungan AHP

1. **Perbandingan Kriteria**

   - Masuk ke menu "Perbandingan AHP"
   - Lakukan perbandingan berpasangan antar kriteria
   - Gunakan skala 1-9 sesuai tingkat kepentingan
   - Pastikan Consistency Ratio ≤ 0.1

2. **Hitung Bobot**
   - Klik tombol "Hitung Bobot" setelah semua perbandingan selesai
   - Sistem akan menghitung bobot setiap kriteria otomatis

#### C. Perhitungan TOPSIS

1. **Hitung Ranking**

   - Masuk ke menu "Ranking TOPSIS"
   - Klik tombol "Hitung Ranking TOPSIS"
   - Sistem akan menampilkan hasil ranking lengkap

2. **Analisis Hasil**
   - Lihat ranking alternatif berdasarkan skor preferensi
   - Analisis detail perhitungan di tab "Detail Perhitungan"

### 3. Manajemen User (Admin)

- Tambah user baru dengan role admin/user
- Edit informasi user
- Reset password user
- Hapus user (kecuali diri sendiri)

## Struktur File

```
├── config/
│   ├── config.php          # Konfigurasi umum aplikasi
│   └── database.php        # Konfigurasi database
├── includes/
│   ├── auth.php           # Class autentikasi
│   ├── ahp.php            # Class metode AHP
│   ├── topsis.php         # Class metode TOPSIS
│   ├── header.php         # Header template
│   └── footer.php         # Footer template
├── assets/
│   └── css/
│       └── style.css      # Custom CSS
├── logs/                  # Folder log aplikasi
├── database.sql           # Struktur database
├── index.php             # Halaman utama (redirect)
├── login.php             # Halaman login
├── logout.php            # Proses logout
├── dashboard.php         # Dashboard utama
├── kriteria.php          # Manajemen kriteria
├── alternatif.php        # Manajemen alternatif
├── nilai_alternatif.php  # Input nilai alternatif
├── perbandingan.php      # Perbandingan AHP
├── ranking.php           # Ranking TOPSIS
├── users.php             # Manajemen users (admin)
├── profile.php           # Profil pengguna
└── README.md             # Dokumentasi
```

## Troubleshooting

### 1. Error Koneksi Database

- Pastikan konfigurasi database di `config/database.php` sudah benar
- Cek apakah MySQL service sudah berjalan
- Pastikan user database memiliki privilege yang cukup

### 2. Error Permission

- Pastikan folder `logs/` dapat ditulis oleh web server
- Set permission yang sesuai: `chmod 755 logs/`

### 3. Session Error

- Pastikan PHP session sudah dikonfigurasi dengan benar
- Cek setting `session.save_path` di php.ini

### 4. Consistency Ratio Tinggi

- Periksa kembali nilai perbandingan antar kriteria
- Pastikan perbandingan logis dan konsisten
- Jika CR > 0.1, revisi perbandingan yang tidak konsisten

## Kontribusi

Untuk melaporkan bug atau request fitur baru, silakan buat issue di repository ini.

## Lisensi

Sistem ini dikembangkan untuk keperluan akademik dan penelitian di Aming Flora.

---

**Dikembangkan dengan ❤️ untuk Aming Flora**
