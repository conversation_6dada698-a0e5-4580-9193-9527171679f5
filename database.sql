-- Database untuk Sistem Pendukung Keputusan Pemilihan Pupuk Organik
-- Menggunakan metode AHP dan TOPSIS

CREATE DATABASE IF NOT EXISTS spk_amingflora;
USE spk_amingflora;

-- Tabel Users
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Kriteria
CREATE TABLE kriteria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kode_kriteria VARCHAR(10) UNIQUE NOT NULL,
    nama_kriteria VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    j<PERSON><PERSON>('benefit', 'cost') DEFAULT 'benefit',
    bobot DECI<PERSON>(10,6) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Alternatif
CREATE TABLE alternatif (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kode_alternatif VARCHAR(10) UNIQUE NOT NULL,
    nama_alternatif VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Perbandingan Kriteria (untuk AHP)
CREATE TABLE perbandingan_kriteria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kriteria1_id INT NOT NULL,
    kriteria2_id INT NOT NULL,
    nilai_perbandingan DECIMAL(10,6) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (kriteria1_id) REFERENCES kriteria(id) ON DELETE CASCADE,
    FOREIGN KEY (kriteria2_id) REFERENCES kriteria(id) ON DELETE CASCADE,
    UNIQUE KEY unique_comparison (kriteria1_id, kriteria2_id)
);

-- Tabel Nilai Alternatif
CREATE TABLE nilai_alternatif (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alternatif_id INT NOT NULL,
    kriteria_id INT NOT NULL,
    nilai DECIMAL(10,6) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (alternatif_id) REFERENCES alternatif(id) ON DELETE CASCADE,
    FOREIGN KEY (kriteria_id) REFERENCES kriteria(id) ON DELETE CASCADE,
    UNIQUE KEY unique_nilai (alternatif_id, kriteria_id)
);

-- Tabel Hasil Perhitungan TOPSIS
CREATE TABLE hasil_topsis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alternatif_id INT NOT NULL,
    skor_preferensi DECIMAL(10,6) NOT NULL,
    ranking INT NOT NULL,
    tanggal_perhitungan TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alternatif_id) REFERENCES alternatif(id) ON DELETE CASCADE
);

-- Insert data default admin
INSERT INTO users (username, password, nama_lengkap, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'admin');

-- Insert data kriteria default
INSERT INTO kriteria (kode_kriteria, nama_kriteria, deskripsi, jenis) VALUES 
('C1', 'Kebutuhan Nutrisi', 'Kemampuan pupuk dalam memenuhi kebutuhan nutrisi tanaman rumput gajah mini', 'benefit'),
('C2', 'Kondisi Tanah', 'Kesesuaian pupuk dengan kondisi tanah di Aming Flora', 'benefit'),
('C3', 'Daya Serap', 'Kemampuan tanaman dalam menyerap nutrisi dari pupuk', 'benefit'),
('C4', 'Biaya', 'Biaya yang diperlukan untuk pengadaan pupuk', 'cost'),
('C5', 'Ketersediaan', 'Kemudahan dalam mendapatkan pupuk di pasaran', 'benefit'),
('C6', 'Dampak Lingkungan', 'Dampak positif pupuk terhadap lingkungan', 'benefit');

-- Insert data alternatif default
INSERT INTO alternatif (kode_alternatif, nama_alternatif, deskripsi) VALUES 
('A1', 'Kompos Bokashi', 'Pupuk organik hasil fermentasi dengan mikroorganisme efektif'),
('A2', 'Pupuk Kandang', 'Pupuk organik dari kotoran hewan ternak yang telah difermentasi'),
('A3', 'Pupuk Organik Cair', 'Pupuk organik dalam bentuk cair yang mudah diserap tanaman'),
('A4', 'Pupuk Organik Padat Komersial', 'Pupuk organik padat yang diproduksi secara komersial');
