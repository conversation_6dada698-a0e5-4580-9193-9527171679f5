<?php
/**
 * Class untuk implementasi metode AHP (Analytical Hierarchy Process)
 */

require_once __DIR__ . '/../config/config.php';

class AHP {
    
    // Skala perbandingan AHP
    public static $skala = [
        1 => 'Sama penting',
        2 => 'Sedikit lebih penting',
        3 => 'Lebih penting',
        4 => 'Sangat lebih penting',
        5 => 'Mutlak lebih penting',
        6 => 'Diantara 5 dan 7',
        7 => 'Sangat mutlak lebih penting',
        8 => '<PERSON>antara 7 dan 9',
        9 => 'Ekstrim lebih penting'
    ];
    
    // Random Index untuk konsistensi
    public static $randomIndex = [
        1 => 0,
        2 => 0,
        3 => 0.58,
        4 => 0.90,
        5 => 1.12,
        6 => 1.24,
        7 => 1.32,
        8 => 1.41,
        9 => 1.45,
        10 => 1.49
    ];
    
    /**
     * Membuat matriks perbandingan berpasangan
     */
    public static function createPairwiseMatrix($kriteria) {
        $n = count($kriteria);
        $matrix = [];
        
        // Inisialisasi matriks dengan nilai 1 (diagonal)
        for ($i = 0; $i < $n; $i++) {
            for ($j = 0; $j < $n; $j++) {
                if ($i == $j) {
                    $matrix[$i][$j] = 1;
                } else {
                    $matrix[$i][$j] = 0;
                }
            }
        }
        
        // Ambil data perbandingan dari database
        $perbandingan = fetchAll("SELECT * FROM perbandingan_kriteria");
        
        foreach ($perbandingan as $p) {
            // Cari index kriteria
            $idx1 = -1;
            $idx2 = -1;
            
            for ($i = 0; $i < $n; $i++) {
                if ($kriteria[$i]['id'] == $p['kriteria1_id']) {
                    $idx1 = $i;
                }
                if ($kriteria[$i]['id'] == $p['kriteria2_id']) {
                    $idx2 = $i;
                }
            }
            
            if ($idx1 >= 0 && $idx2 >= 0) {
                $matrix[$idx1][$idx2] = $p['nilai_perbandingan'];
                $matrix[$idx2][$idx1] = 1 / $p['nilai_perbandingan'];
            }
        }
        
        return $matrix;
    }
    
    /**
     * Menghitung bobot menggunakan metode Eigenvector
     */
    public static function calculateWeights($matrix) {
        $n = count($matrix);
        $weights = [];
        
        // Hitung jumlah setiap kolom
        $columnSums = [];
        for ($j = 0; $j < $n; $j++) {
            $sum = 0;
            for ($i = 0; $i < $n; $i++) {
                $sum += $matrix[$i][$j];
            }
            $columnSums[$j] = $sum;
        }
        
        // Normalisasi matriks
        $normalizedMatrix = [];
        for ($i = 0; $i < $n; $i++) {
            for ($j = 0; $j < $n; $j++) {
                $normalizedMatrix[$i][$j] = $matrix[$i][$j] / $columnSums[$j];
            }
        }
        
        // Hitung rata-rata setiap baris (bobot)
        for ($i = 0; $i < $n; $i++) {
            $sum = 0;
            for ($j = 0; $j < $n; $j++) {
                $sum += $normalizedMatrix[$i][$j];
            }
            $weights[$i] = $sum / $n;
        }
        
        return $weights;
    }
    
    /**
     * Menghitung Consistency Ratio (CR)
     */
    public static function calculateConsistencyRatio($matrix, $weights) {
        $n = count($matrix);
        
        if ($n <= 2) {
            return 0; // Matriks 2x2 selalu konsisten
        }
        
        // Hitung lambda max
        $lambdaMax = 0;
        for ($i = 0; $i < $n; $i++) {
            $sum = 0;
            for ($j = 0; $j < $n; $j++) {
                $sum += $matrix[$i][$j] * $weights[$j];
            }
            $lambdaMax += $sum / $weights[$i];
        }
        $lambdaMax = $lambdaMax / $n;
        
        // Hitung Consistency Index (CI)
        $ci = ($lambdaMax - $n) / ($n - 1);
        
        // Hitung Consistency Ratio (CR)
        $ri = self::$randomIndex[$n] ?? 1.49;
        $cr = $ci / $ri;
        
        return $cr;
    }
    
    /**
     * Simpan bobot ke database
     */
    public static function saveWeights($kriteria, $weights) {
        try {
            $conn = getDBConnection();
            $conn->beginTransaction();
            
            for ($i = 0; $i < count($kriteria); $i++) {
                $query = "UPDATE kriteria SET bobot = ? WHERE id = ?";
                executeUpdate($query, [$weights[$i], $kriteria[$i]['id']]);
            }
            
            $conn->commit();
            return true;
        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }
    }
    
    /**
     * Validasi kelengkapan matriks perbandingan
     */
    public static function validateMatrix($kriteria) {
        $n = count($kriteria);
        $requiredComparisons = ($n * ($n - 1)) / 2;
        
        $existingComparisons = fetchOne("SELECT COUNT(*) as total FROM perbandingan_kriteria")['total'];
        
        return [
            'complete' => $existingComparisons >= $requiredComparisons,
            'existing' => $existingComparisons,
            'required' => $requiredComparisons,
            'missing' => max(0, $requiredComparisons - $existingComparisons)
        ];
    }
    
    /**
     * Generate missing comparisons
     */
    public static function generateMissingComparisons($kriteria) {
        $missing = [];
        $n = count($kriteria);
        
        // Ambil perbandingan yang sudah ada
        $existing = [];
        $perbandingan = fetchAll("SELECT kriteria1_id, kriteria2_id FROM perbandingan_kriteria");
        
        foreach ($perbandingan as $p) {
            $existing[$p['kriteria1_id']][$p['kriteria2_id']] = true;
            $existing[$p['kriteria2_id']][$p['kriteria1_id']] = true;
        }
        
        // Cari yang belum ada
        for ($i = 0; $i < $n; $i++) {
            for ($j = $i + 1; $j < $n; $j++) {
                $id1 = $kriteria[$i]['id'];
                $id2 = $kriteria[$j]['id'];
                
                if (!isset($existing[$id1][$id2])) {
                    $missing[] = [
                        'kriteria1' => $kriteria[$i],
                        'kriteria2' => $kriteria[$j]
                    ];
                }
            }
        }
        
        return $missing;
    }
    
    /**
     * Reset semua perbandingan
     */
    public static function resetComparisons() {
        executeUpdate("DELETE FROM perbandingan_kriteria");
        executeUpdate("UPDATE kriteria SET bobot = 0");
    }
    
    /**
     * Get comparison description
     */
    public static function getComparisonDescription($value) {
        $intValue = (int)$value;
        return self::$skala[$intValue] ?? 'Nilai tidak valid';
    }
    
    /**
     * Format matrix untuk display
     */
    public static function formatMatrixForDisplay($matrix, $kriteria) {
        $formatted = [];
        $n = count($matrix);
        
        for ($i = 0; $i < $n; $i++) {
            $formatted[$i] = [];
            for ($j = 0; $j < $n; $j++) {
                if ($i == $j) {
                    $formatted[$i][$j] = '1.0000';
                } else {
                    $formatted[$i][$j] = number_format($matrix[$i][$j], 4);
                }
            }
        }
        
        return $formatted;
    }
}
?>
