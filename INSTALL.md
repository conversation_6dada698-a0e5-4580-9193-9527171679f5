# Panduan Instalasi SPK Pupuk Organik

## Persyaratan Sistem

### Minimum Requirements

- **PHP**: 7.4 atau lebih baru
- **Database**: MySQL 5.7+ atau MariaDB 10.2+
- **Web Server**: Apache 2.4+ atau Nginx 1.18+
- **RAM**: 512 MB
- **Storage**: 100 MB free space

### PHP Extensions Required

- PDO
- PDO_MySQL
- mbstring
- json
- session

## Langkah Instalasi

### 1. Persiapan Environment

#### Untuk XAMPP (Windows/Mac/Linux)

1. Download dan install XAMPP dari https://www.apachefriends.org/
2. Start Apache dan MySQL service
3. Buka phpMyAdmin di http://localhost/phpmyadmin

#### Untuk LARAGON (Windows)

1. Download dan install Laragon dari https://laragon.org/
2. Start All services
3. Buka phpMyAdmin melalui menu Laragon

#### Untuk Linux (Ubuntu/Debian)

```bash
# Install Apache, MySQL, PHP
sudo apt update
sudo apt install apache2 mysql-server php php-mysql php-mbstring

# Start services
sudo systemctl start apache2
sudo systemctl start mysql
```

### 2. Setup Database

#### Metode 1: Menggunakan phpMyAdmin

1. Buka phpMyAdmin di browser
2. Klik tab "Databases"
3. Buat database baru dengan nama `spk_amingflora`
4. Pilih database yang baru dibuat
5. Klik tab "Import"
6. Pilih file `database.sql` dari folder aplikasi
7. Klik "Go" untuk mengimport

#### Metode 2: Menggunakan Command Line

```bash
# Login ke MySQL
mysql -u root -p

# Buat database
CREATE DATABASE spk_amingflora;
exit;

# Import struktur database
mysql -u root -p spk_amingflora < database.sql
```

### 3. Konfigurasi Aplikasi

#### Edit Konfigurasi Database

1. Buka file `config/database.php`
2. Sesuaikan konfigurasi dengan setup database Anda:

```php
private $host = 'localhost';        // Host database
private $db_name = 'spk_amingflora';  // Nama database
private $username = 'root';         // Username database
private $password = '';             // Password database (kosong untuk XAMPP default)
```

#### Untuk Production Server

```php
private $host = 'your_host';
private $db_name = 'your_database';
private $username = 'your_username';
private $password = 'your_strong_password';
```

### 4. Upload File ke Server

#### Untuk Local Development

1. Copy semua file aplikasi ke folder web server:
   - **XAMPP**: `C:\xampp\htdocs\spk-pupuk\`
   - **LARAGON**: `C:\laragon\www\spk-pupuk\`
   - **Linux**: `/var/www/html/spk-pupuk/`

#### Untuk Production Server

1. Upload semua file via FTP/SFTP ke folder public_html atau www
2. Pastikan struktur folder tetap utuh

### 5. Set Permissions (Linux/Mac)

```bash
# Set permission untuk folder logs
chmod 755 logs/

# Set ownership (jika diperlukan)
sudo chown -R www-data:www-data /var/www/html/spk-pupuk/
```

### 6. Konfigurasi Web Server

#### Apache (.htaccess)

File `.htaccess` tidak diperlukan karena aplikasi menggunakan routing sederhana.

#### Nginx

Jika menggunakan Nginx, tambahkan konfigurasi berikut:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/spk-pupuk;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## Testing Instalasi

### 1. Akses Aplikasi

1. Buka browser dan akses:

   - Local: `http://localhost/spk-pupuk/`
   - Production: `http://your-domain.com/`

2. Anda akan diarahkan ke halaman login

### 2. Login Pertama Kali

- **Username**: `admin`
- **Password**: `password`

### 3. Verifikasi Fitur

1. **Dashboard**: Pastikan statistik muncul dengan benar
2. **Kriteria**: Coba tambah/edit kriteria
3. **Alternatif**: Coba tambah/edit alternatif
4. **Nilai Alternatif**: Input beberapa nilai test
5. **Perbandingan AHP**: Lakukan perbandingan kriteria
6. **Ranking TOPSIS**: Hitung ranking

## Troubleshooting

### Error: "Connection failed"

**Penyebab**: Konfigurasi database salah atau MySQL tidak berjalan

**Solusi**:

1. Cek konfigurasi di `config/database.php`
2. Pastikan MySQL service berjalan
3. Test koneksi database secara manual

### Error: "Permission denied" pada folder logs

**Penyebab**: Web server tidak memiliki permission untuk menulis ke folder logs

**Solusi**:

```bash
chmod 755 logs/
# atau
chmod 777 logs/  # untuk development saja
```

### Error: "Session could not be started"

**Penyebab**: Konfigurasi session PHP bermasalah

**Solusi**:

1. Cek `session.save_path` di php.ini
2. Pastikan folder session dapat ditulis
3. Restart web server

### Error: "Class not found"

**Penyebab**: File include tidak ditemukan atau path salah

**Solusi**:

1. Pastikan semua file terupload dengan benar
2. Cek struktur folder sesuai dokumentasi
3. Pastikan case-sensitive filename (untuk Linux)

### Database Import Error

**Penyebab**: Versi MySQL tidak kompatibel atau syntax error

**Solusi**:

1. Pastikan menggunakan MySQL 5.7+ atau MariaDB 10.2+
2. Import ulang file database.sql
3. Cek log error MySQL untuk detail

## Konfigurasi Production

### 1. Security Settings

```php
// Di config/config.php, ubah untuk production:
define('BASE_URL', 'https://your-domain.com/');

// Aktifkan HTTPS redirect jika diperlukan
if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
    $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header("Location: $redirectURL");
    exit();
}
```

### 2. Database Security

1. Buat user database khusus (jangan gunakan root)
2. Berikan privilege minimal yang diperlukan
3. Gunakan password yang kuat

```sql
CREATE USER 'spk_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON spk_amingflora.* TO 'spk_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. PHP Configuration

Edit `php.ini` untuk production:

```ini
display_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
session.cookie_secure = 1
session.cookie_httponly = 1
```

### 4. Backup Strategy

1. Setup automated database backup
2. Backup file aplikasi secara berkala
3. Test restore procedure

## Update Aplikasi

### 1. Backup Data

```bash
# Backup database
mysqldump -u username -p spk_amingflora > backup_$(date +%Y%m%d).sql

# Backup files
tar -czf backup_files_$(date +%Y%m%d).tar.gz /path/to/application/
```

### 2. Update Files

1. Download versi terbaru
2. Replace file aplikasi (kecuali config dan logs)
3. Jalankan script update database jika ada

### 3. Test Update

1. Cek semua fitur masih berfungsi
2. Verifikasi data tidak hilang
3. Test performa aplikasi

---

**Jika mengalami kesulitan instalasi, silakan hubungi tim support atau buat issue di repository.**
