<?php
// Pastikan user sudah login
requireLogin();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Chart.js untuk grafik -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- Logo/Brand -->
                    <div class="text-center mb-4">
                        <i class="fas fa-seedling fa-3x text-white mb-2"></i>
                        <h5 class="text-white">SPK Pupuk Organik</h5>
                        <small class="text-white-50">Aming Flora</small>
                    </div>
                    
                    <!-- User Info -->
                    <div class="text-center mb-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px; margin: 0 10px;">
                        <i class="fas fa-user-circle fa-2x text-white mb-2"></i>
                        <div class="text-white">
                            <strong><?php echo $_SESSION['nama_lengkap']; ?></strong>
                            <br>
                            <small class="text-white-50"><?php echo ucfirst($_SESSION['role']); ?></small>
                        </div>
                    </div>
                    
                    <!-- Navigation Menu -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" 
                               href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        
                        <?php if (isAdmin()): ?>
                        <!-- Menu Admin -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" 
                               href="users.php">
                                <i class="fas fa-users"></i>
                                Manajemen User
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'kriteria.php' ? 'active' : ''; ?>" 
                               href="kriteria.php">
                                <i class="fas fa-list-alt"></i>
                                Kriteria
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'alternatif.php' ? 'active' : ''; ?>" 
                               href="alternatif.php">
                                <i class="fas fa-leaf"></i>
                                Alternatif
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'nilai_alternatif.php' ? 'active' : ''; ?>" 
                               href="nilai_alternatif.php">
                                <i class="fas fa-calculator"></i>
                                Nilai Alternatif
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'perbandingan.php' ? 'active' : ''; ?>" 
                               href="perbandingan.php">
                                <i class="fas fa-balance-scale"></i>
                                Perbandingan AHP
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'ranking.php' ? 'active' : ''; ?>" 
                               href="ranking.php">
                                <i class="fas fa-trophy"></i>
                                Ranking TOPSIS
                            </a>
                        </li>
                        
                        <li class="nav-item mt-3">
                            <hr class="text-white-50">
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user-cog"></i>
                                Profil
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php" onclick="return confirm('Yakin ingin logout?')">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <header class="main-header d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <div class="d-flex align-items-center">
                        <!-- Mobile menu toggle -->
                        <button class="btn btn-outline-primary d-md-none me-3" type="button" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        
                        <h1 class="h2 mb-0"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
                    </div>
                    
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i>
                                <?php echo $_SESSION['nama_lengkap']; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-cog me-2"></i>Profil
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php" onclick="return confirm('Yakin ingin logout?')">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </header>
                
                <!-- Alert Messages -->
                <?php 
                $alert = getAlert();
                if ($alert): 
                ?>
                <div class="alert alert-<?php echo $alert['type']; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-<?php echo $alert['type'] == 'success' ? 'check-circle' : ($alert['type'] == 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                    <?php echo $alert['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Page Content -->
                <div class="content-wrapper fade-in">
