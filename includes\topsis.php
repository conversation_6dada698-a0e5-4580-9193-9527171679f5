<?php
/**
 * Class untuk implementasi metode TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)
 */

require_once __DIR__ . '/../config/config.php';

class TOPSIS {
    
    /**
     * Membuat matriks keputusan
     */
    public static function createDecisionMatrix($alternatif, $kriteria) {
        $matrix = [];
        
        foreach ($alternatif as $i => $alt) {
            $matrix[$i] = [];
            foreach ($kriteria as $j => $krit) {
                // Ambil nilai dari database
                $nilai = fetchOne("SELECT nilai FROM nilai_alternatif WHERE alternatif_id = ? AND kriteria_id = ?", 
                                [$alt['id'], $krit['id']]);
                
                $matrix[$i][$j] = $nilai ? (float)$nilai['nilai'] : 0;
            }
        }
        
        return $matrix;
    }
    
    /**
     * Normalisasi matriks menggunakan metode vektor
     */
    public static function normalizeMatrix($matrix) {
        $m = count($matrix);    // jumlah alternatif
        $n = count($matrix[0]); // jumlah kriteria
        $normalized = [];
        
        // Hitung akar kuadrat dari jumlah kuadrat setiap kolom
        $columnSums = [];
        for ($j = 0; $j < $n; $j++) {
            $sum = 0;
            for ($i = 0; $i < $m; $i++) {
                $sum += pow($matrix[$i][$j], 2);
            }
            $columnSums[$j] = sqrt($sum);
        }
        
        // Normalisasi
        for ($i = 0; $i < $m; $i++) {
            $normalized[$i] = [];
            for ($j = 0; $j < $n; $j++) {
                if ($columnSums[$j] != 0) {
                    $normalized[$i][$j] = $matrix[$i][$j] / $columnSums[$j];
                } else {
                    $normalized[$i][$j] = 0;
                }
            }
        }
        
        return $normalized;
    }
    
    /**
     * Membuat matriks keputusan terbobot
     */
    public static function createWeightedMatrix($normalizedMatrix, $weights) {
        $m = count($normalizedMatrix);
        $n = count($normalizedMatrix[0]);
        $weighted = [];
        
        for ($i = 0; $i < $m; $i++) {
            $weighted[$i] = [];
            for ($j = 0; $j < $n; $j++) {
                $weighted[$i][$j] = $normalizedMatrix[$i][$j] * $weights[$j];
            }
        }
        
        return $weighted;
    }
    
    /**
     * Menentukan solusi ideal positif dan negatif
     */
    public static function findIdealSolutions($weightedMatrix, $kriteria) {
        $m = count($weightedMatrix);
        $n = count($weightedMatrix[0]);
        
        $idealPositive = [];
        $idealNegative = [];
        
        for ($j = 0; $j < $n; $j++) {
            $columnValues = [];
            for ($i = 0; $i < $m; $i++) {
                $columnValues[] = $weightedMatrix[$i][$j];
            }
            
            if ($kriteria[$j]['jenis'] == 'benefit') {
                // Untuk kriteria benefit: max untuk ideal positif, min untuk ideal negatif
                $idealPositive[$j] = max($columnValues);
                $idealNegative[$j] = min($columnValues);
            } else {
                // Untuk kriteria cost: min untuk ideal positif, max untuk ideal negatif
                $idealPositive[$j] = min($columnValues);
                $idealNegative[$j] = max($columnValues);
            }
        }
        
        return [
            'positive' => $idealPositive,
            'negative' => $idealNegative
        ];
    }
    
    /**
     * Menghitung jarak ke solusi ideal
     */
    public static function calculateDistances($weightedMatrix, $idealSolutions) {
        $m = count($weightedMatrix);
        $n = count($weightedMatrix[0]);
        
        $distances = [
            'positive' => [],
            'negative' => []
        ];
        
        for ($i = 0; $i < $m; $i++) {
            // Jarak ke solusi ideal positif
            $sumPositive = 0;
            for ($j = 0; $j < $n; $j++) {
                $sumPositive += pow($weightedMatrix[$i][$j] - $idealSolutions['positive'][$j], 2);
            }
            $distances['positive'][$i] = sqrt($sumPositive);
            
            // Jarak ke solusi ideal negatif
            $sumNegative = 0;
            for ($j = 0; $j < $n; $j++) {
                $sumNegative += pow($weightedMatrix[$i][$j] - $idealSolutions['negative'][$j], 2);
            }
            $distances['negative'][$i] = sqrt($sumNegative);
        }
        
        return $distances;
    }
    
    /**
     * Menghitung skor preferensi
     */
    public static function calculatePreferenceScores($distances) {
        $scores = [];
        $m = count($distances['positive']);
        
        for ($i = 0; $i < $m; $i++) {
            $denominator = $distances['positive'][$i] + $distances['negative'][$i];
            if ($denominator != 0) {
                $scores[$i] = $distances['negative'][$i] / $denominator;
            } else {
                $scores[$i] = 0;
            }
        }
        
        return $scores;
    }
    
    /**
     * Mengurutkan alternatif berdasarkan skor preferensi
     */
    public static function rankAlternatives($alternatif, $scores) {
        $ranking = [];
        
        for ($i = 0; $i < count($alternatif); $i++) {
            $ranking[] = [
                'alternatif' => $alternatif[$i],
                'skor' => $scores[$i]
            ];
        }
        
        // Sort descending berdasarkan skor
        usort($ranking, function($a, $b) {
            return $b['skor'] <=> $a['skor'];
        });
        
        // Tambahkan ranking
        foreach ($ranking as $index => &$item) {
            $item['ranking'] = $index + 1;
        }
        
        return $ranking;
    }
    
    /**
     * Simpan hasil ranking ke database
     */
    public static function saveRanking($ranking) {
        try {
            $conn = getDBConnection();
            $conn->beginTransaction();
            
            // Hapus hasil sebelumnya
            executeUpdate("DELETE FROM hasil_topsis");
            
            // Simpan hasil baru
            foreach ($ranking as $item) {
                $query = "INSERT INTO hasil_topsis (alternatif_id, skor_preferensi, ranking) VALUES (?, ?, ?)";
                insertData($query, [
                    $item['alternatif']['id'],
                    $item['skor'],
                    $item['ranking']
                ]);
            }
            
            $conn->commit();
            return true;
        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }
    }
    
    /**
     * Validasi kelengkapan data untuk TOPSIS
     */
    public static function validateData($alternatif, $kriteria) {
        $errors = [];
        
        if (count($alternatif) < 2) {
            $errors[] = 'Minimal harus ada 2 alternatif';
        }
        
        if (count($kriteria) < 2) {
            $errors[] = 'Minimal harus ada 2 kriteria';
        }
        
        // Cek apakah semua kriteria memiliki bobot
        $bobotKosong = 0;
        foreach ($kriteria as $k) {
            if ($k['bobot'] == 0) {
                $bobotKosong++;
            }
        }
        
        if ($bobotKosong > 0) {
            $errors[] = "Ada $bobotKosong kriteria yang belum memiliki bobot. Lakukan perhitungan AHP terlebih dahulu.";
        }
        
        // Cek kelengkapan nilai alternatif
        $totalNilai = count($alternatif) * count($kriteria);
        $nilaiAda = fetchOne("SELECT COUNT(*) as total FROM nilai_alternatif")['total'];
        
        if ($nilaiAda < $totalNilai) {
            $kurang = $totalNilai - $nilaiAda;
            $errors[] = "Masih kurang $kurang nilai alternatif. Lengkapi data nilai terlebih dahulu.";
        }
        
        return $errors;
    }
    
    /**
     * Proses lengkap TOPSIS
     */
    public static function process($alternatif, $kriteria) {
        // Validasi data
        $errors = self::validateData($alternatif, $kriteria);
        if (!empty($errors)) {
            throw new Exception(implode('; ', $errors));
        }
        
        // Ambil bobot dari kriteria
        $weights = array_column($kriteria, 'bobot');
        
        // 1. Buat matriks keputusan
        $decisionMatrix = self::createDecisionMatrix($alternatif, $kriteria);
        
        // 2. Normalisasi matriks
        $normalizedMatrix = self::normalizeMatrix($decisionMatrix);
        
        // 3. Buat matriks terbobot
        $weightedMatrix = self::createWeightedMatrix($normalizedMatrix, $weights);
        
        // 4. Tentukan solusi ideal
        $idealSolutions = self::findIdealSolutions($weightedMatrix, $kriteria);
        
        // 5. Hitung jarak
        $distances = self::calculateDistances($weightedMatrix, $idealSolutions);
        
        // 6. Hitung skor preferensi
        $scores = self::calculatePreferenceScores($distances);
        
        // 7. Ranking
        $ranking = self::rankAlternatives($alternatif, $scores);
        
        // 8. Simpan hasil
        self::saveRanking($ranking);
        
        return [
            'decisionMatrix' => $decisionMatrix,
            'normalizedMatrix' => $normalizedMatrix,
            'weightedMatrix' => $weightedMatrix,
            'idealSolutions' => $idealSolutions,
            'distances' => $distances,
            'scores' => $scores,
            'ranking' => $ranking
        ];
    }
    
    /**
     * Format matrix untuk display
     */
    public static function formatMatrixForDisplay($matrix, $decimals = 4) {
        $formatted = [];
        for ($i = 0; $i < count($matrix); $i++) {
            $formatted[$i] = [];
            for ($j = 0; $j < count($matrix[$i]); $j++) {
                $formatted[$i][$j] = number_format($matrix[$i][$j], $decimals);
            }
        }
        return $formatted;
    }
}
?>
