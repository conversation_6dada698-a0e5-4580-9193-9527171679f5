<?php
require_once 'config/config.php';

$pageTitle = 'Manajemen Alternatif';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $kode = sanitize($_POST['kode_alternatif']);
                    $nama = sanitize($_POST['nama_alternatif']);
                    $deskripsi = sanitize($_POST['deskripsi']);
                    
                    if (empty($kode) || empty($nama)) {
                        throw new Exception('Kode dan nama alternatif harus diisi');
                    }
                    
                    // Cek duplikasi kode
                    $existing = fetchOne("SELECT id FROM alternatif WHERE kode_alternatif = ?", [$kode]);
                    if ($existing) {
                        throw new Exception('Kode alternatif sudah ada');
                    }
                    
                    $query = "INSERT INTO alternatif (kode_alternatif, nama_alternatif, deskripsi) VALUES (?, ?, ?)";
                    insertData($query, [$kode, $nama, $deskripsi]);
                    
                    setAlert('success', 'Alternatif berhasil ditambahkan');
                    break;
                    
                case 'edit':
                    $id = (int)$_POST['id'];
                    $kode = sanitize($_POST['kode_alternatif']);
                    $nama = sanitize($_POST['nama_alternatif']);
                    $deskripsi = sanitize($_POST['deskripsi']);
                    
                    if (empty($kode) || empty($nama)) {
                        throw new Exception('Kode dan nama alternatif harus diisi');
                    }
                    
                    // Cek duplikasi kode
                    $existing = fetchOne("SELECT id FROM alternatif WHERE kode_alternatif = ? AND id != ?", [$kode, $id]);
                    if ($existing) {
                        throw new Exception('Kode alternatif sudah ada');
                    }
                    
                    $query = "UPDATE alternatif SET kode_alternatif = ?, nama_alternatif = ?, deskripsi = ? WHERE id = ?";
                    executeUpdate($query, [$kode, $nama, $deskripsi, $id]);
                    
                    setAlert('success', 'Alternatif berhasil diupdate');
                    break;
                    
                case 'delete':
                    $id = (int)$_POST['id'];
                    
                    // Cek apakah alternatif memiliki nilai
                    $used = fetchOne("SELECT COUNT(*) as total FROM nilai_alternatif WHERE alternatif_id = ?", [$id]);
                    if ($used['total'] > 0) {
                        throw new Exception('Alternatif tidak dapat dihapus karena sudah memiliki nilai');
                    }
                    
                    executeUpdate("DELETE FROM alternatif WHERE id = ?", [$id]);
                    setAlert('success', 'Alternatif berhasil dihapus');
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('alternatif.php');
}

// Ambil data alternatif dengan statistik
$alternatif = fetchAll("
    SELECT a.*, 
           COUNT(na.id) as jumlah_nilai,
           AVG(na.nilai) as rata_nilai
    FROM alternatif a
    LEFT JOIN nilai_alternatif na ON a.id = na.alternatif_id
    GROUP BY a.id
    ORDER BY a.kode_alternatif
");

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-leaf me-2"></i>Data Alternatif Pupuk Organik
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                    <i class="fas fa-plus me-2"></i>Tambah Alternatif
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Kode</th>
                                <th>Nama Alternatif</th>
                                <th>Deskripsi</th>
                                <th>Jumlah Nilai</th>
                                <th>Rata-rata</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($alternatif)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada data alternatif</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($alternatif as $index => $item): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $item['kode_alternatif']; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo $item['nama_alternatif']; ?></strong>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo strlen($item['deskripsi']) > 60 ? substr($item['deskripsi'], 0, 60) . '...' : $item['deskripsi']; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $item['jumlah_nilai']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($item['rata_nilai']): ?>
                                                <span class="badge bg-warning"><?php echo formatNumber($item['rata_nilai'], 2); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-info" 
                                                        onclick="editAlternatif(<?php echo htmlspecialchars(json_encode($item)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteAlternatif(<?php echo $item['id']; ?>, '<?php echo $item['nama_alternatif']; ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Tambah Alternatif
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="kode_alternatif" class="form-label">Kode Alternatif</label>
                                <input type="text" class="form-control" id="kode_alternatif" name="kode_alternatif" 
                                       placeholder="Contoh: A1" required maxlength="10">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nama_alternatif" class="form-label">Nama Alternatif</label>
                                <input type="text" class="form-control" id="nama_alternatif" name="nama_alternatif" 
                                       placeholder="Nama pupuk organik" required maxlength="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="deskripsi" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Deskripsi lengkap tentang pupuk organik ini..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Alternatif
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_kode_alternatif" class="form-label">Kode Alternatif</label>
                                <input type="text" class="form-control" id="edit_kode_alternatif" name="kode_alternatif" 
                                       required maxlength="10">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_nama_alternatif" class="form-label">Nama Alternatif</label>
                                <input type="text" class="form-control" id="edit_nama_alternatif" name="nama_alternatif" 
                                       required maxlength="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_deskripsi" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="edit_deskripsi" name="deskripsi" rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$pageScripts = "
<script>
function editAlternatif(data) {
    document.getElementById('edit_id').value = data.id;
    document.getElementById('edit_kode_alternatif').value = data.kode_alternatif;
    document.getElementById('edit_nama_alternatif').value = data.nama_alternatif;
    document.getElementById('edit_deskripsi').value = data.deskripsi || '';
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteAlternatif(id, nama) {
    if (confirm('Yakin ingin menghapus alternatif \"' + nama + '\"?\\n\\nPerhatian: Semua data nilai yang terkait akan ikut terhapus.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
