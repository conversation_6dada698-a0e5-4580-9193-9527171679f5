<?php
/**
 * File test untuk memastikan koneksi database dan path sudah benar
 */

echo "<h2>Test Koneksi SPK Aming Flora</h2>";

// Test include config
try {
    require_once 'config/config.php';
    echo "✅ Config berhasil dimuat<br>";
} catch (Exception $e) {
    echo "❌ Error loading config: " . $e->getMessage() . "<br>";
    exit;
}

// Test koneksi database
try {
    $conn = getDBConnection();
    if ($conn) {
        echo "✅ Koneksi database berhasil<br>";
        
        // Test query sederhana
        $result = $conn->query("SELECT COUNT(*) as total FROM users");
        if ($result) {
            $row = $result->fetch(PDO::FETCH_ASSOC);
            echo "✅ Query test berhasil - Total users: " . $row['total'] . "<br>";
        }
    } else {
        echo "❌ Koneksi database gagal<br>";
    }
} catch (Exception $e) {
    echo "❌ Error database: " . $e->getMessage() . "<br>";
}

// Test include auth
try {
    require_once 'includes/auth.php';
    echo "✅ Auth class berhasil dimuat<br>";
} catch (Exception $e) {
    echo "❌ Error loading auth: " . $e->getMessage() . "<br>";
}

// Test include AHP
try {
    require_once 'includes/ahp.php';
    echo "✅ AHP class berhasil dimuat<br>";
} catch (Exception $e) {
    echo "❌ Error loading AHP: " . $e->getMessage() . "<br>";
}

// Test include TOPSIS
try {
    require_once 'includes/topsis.php';
    echo "✅ TOPSIS class berhasil dimuat<br>";
} catch (Exception $e) {
    echo "❌ Error loading TOPSIS: " . $e->getMessage() . "<br>";
}

// Test folder logs
if (is_dir('logs') && is_writable('logs')) {
    echo "✅ Folder logs tersedia dan dapat ditulis<br>";
} else {
    echo "❌ Folder logs tidak tersedia atau tidak dapat ditulis<br>";
}

echo "<br><strong>Jika semua test berhasil (✅), sistem siap digunakan!</strong><br>";
echo "<a href='index.php'>Ke Halaman Utama</a>";
?>
