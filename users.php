<?php
require_once 'config/config.php';
require_once 'includes/auth.php';

// <PERSON>ya admin yang bisa akses
requireAdmin();

$pageTitle = 'Manajemen Users';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $data = [
                        'username' => sanitize($_POST['username']),
                        'password' => $_POST['password'],
                        'confirm_password' => $_POST['confirm_password'],
                        'nama_lengkap' => sanitize($_POST['nama_lengkap']),
                        'email' => sanitize($_POST['email']),
                        'role' => sanitize($_POST['role'])
                    ];
                    
                    $result = Auth::register($data);
                    if ($result['success']) {
                        setAlert('success', $result['message']);
                    } else {
                        setAlert('danger', $result['message']);
                    }
                    break;
                    
                case 'edit':
                    $id = (int)$_POST['id'];
                    $username = sanitize($_POST['username']);
                    $nama_lengkap = sanitize($_POST['nama_lengkap']);
                    $email = sanitize($_POST['email']);
                    $role = sanitize($_POST['role']);
                    
                    if (empty($username) || empty($nama_lengkap) || empty($email)) {
                        throw new Exception('Semua field harus diisi');
                    }
                    
                    if (!isValidEmail($email)) {
                        throw new Exception('Format email tidak valid');
                    }
                    
                    // Cek duplikasi username/email (kecuali untuk user yang sedang diedit)
                    $existing = fetchOne("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?", 
                                       [$username, $email, $id]);
                    if ($existing) {
                        throw new Exception('Username atau email sudah digunakan');
                    }
                    
                    $query = "UPDATE users SET username = ?, nama_lengkap = ?, email = ?, role = ? WHERE id = ?";
                    executeUpdate($query, [$username, $nama_lengkap, $email, $role, $id]);
                    
                    setAlert('success', 'User berhasil diupdate');
                    break;
                    
                case 'delete':
                    $id = (int)$_POST['id'];
                    
                    // Tidak bisa hapus diri sendiri
                    if ($id == $_SESSION['user_id']) {
                        throw new Exception('Tidak dapat menghapus akun sendiri');
                    }
                    
                    executeUpdate("DELETE FROM users WHERE id = ?", [$id]);
                    setAlert('success', 'User berhasil dihapus');
                    break;
                    
                case 'reset_password':
                    $id = (int)$_POST['id'];
                    $newPassword = 'password123'; // Default password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    
                    executeUpdate("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $id]);
                    setAlert('success', "Password berhasil direset ke: $newPassword");
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('users.php');
}

// Ambil data users
$users = fetchAll("SELECT * FROM users ORDER BY created_at DESC");

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Data Users
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                    <i class="fas fa-plus me-2"></i>Tambah User
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Username</th>
                                <th>Nama Lengkap</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Terdaftar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada data user</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($users as $index => $user): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <strong><?php echo $user['username']; ?></strong>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="badge bg-info ms-1">You</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $user['nama_lengkap']; ?></td>
                                        <td><?php echo $user['email']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['role'] == 'admin' ? 'danger' : 'success'; ?>">
                                                <?php echo ucfirst($user['role']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-info" 
                                                        onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning" 
                                                        onclick="resetPassword(<?php echo $user['id']; ?>, '<?php echo $user['username']; ?>')">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo $user['username']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Tambah User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" 
                               required maxlength="50" placeholder="Username unik">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       required minlength="6" placeholder="Minimal 6 karakter">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Konfirmasi Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       required placeholder="Ulangi password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="nama_lengkap" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" 
                               required maxlength="100" placeholder="Nama lengkap user">
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               required maxlength="100" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Pilih role</option>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" 
                               required maxlength="50">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_nama_lengkap" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="edit_nama_lengkap" name="nama_lengkap" 
                               required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" 
                               required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$pageScripts = "
<script>
function editUser(data) {
    document.getElementById('edit_id').value = data.id;
    document.getElementById('edit_username').value = data.username;
    document.getElementById('edit_nama_lengkap').value = data.nama_lengkap;
    document.getElementById('edit_email').value = data.email;
    document.getElementById('edit_role').value = data.role;
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteUser(id, username) {
    if (confirm('Yakin ingin menghapus user \"' + username + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function resetPassword(id, username) {
    if (confirm('Yakin ingin reset password untuk user \"' + username + '\"?\\n\\nPassword akan direset ke: password123')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"reset_password\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

// Validasi konfirmasi password
document.getElementById('confirm_password').addEventListener('blur', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Password tidak cocok');
    } else {
        this.setCustomValidity('');
    }
});
</script>
";

include 'includes/footer.php';
?>
