<?php
/**
 * Konfigurasi Database untuk SPK Pupuk Organik
 * Sistem Pendukung Keputusan dengan metode AHP-TOPSIS
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'spk_amingflora';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * Fungsi helper untuk koneksi database
 */
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

/**
 * Fungsi untuk mengeksekusi query dengan prepared statement
 */
function executeQuery($query, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        throw new Exception("Database error: " . $e->getMessage());
    }
}

/**
 * Fungsi untuk mengambil satu baris data
 */
function fetchOne($query, $params = []) {
    $stmt = executeQuery($query, $params);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Fungsi untuk mengambil semua baris data
 */
function fetchAll($query, $params = []) {
    $stmt = executeQuery($query, $params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Fungsi untuk insert data dan return last insert id
 */
function insertData($query, $params = []) {
    $conn = getDBConnection();
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    return $conn->lastInsertId();
}

/**
 * Fungsi untuk update/delete data dan return affected rows
 */
function executeUpdate($query, $params = []) {
    $stmt = executeQuery($query, $params);
    return $stmt->rowCount();
}
?>
