<?php
require_once 'config/config.php';
require_once 'includes/auth.php';

$pageTitle = 'Profil Saya';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_profile':
                    $nama_lengkap = sanitize($_POST['nama_lengkap']);
                    $email = sanitize($_POST['email']);
                    
                    if (empty($nama_lengkap) || empty($email)) {
                        throw new Exception('Nama lengkap dan email harus diisi');
                    }
                    
                    if (!isValidEmail($email)) {
                        throw new Exception('Format email tidak valid');
                    }
                    
                    // Cek duplikasi email (kecuali untuk user yang sedang login)
                    $existing = fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", 
                                       [$email, $_SESSION['user_id']]);
                    if ($existing) {
                        throw new Exception('Email sudah digunakan oleh user lain');
                    }
                    
                    $query = "UPDATE users SET nama_lengkap = ?, email = ? WHERE id = ?";
                    executeUpdate($query, [$nama_lengkap, $email, $_SESSION['user_id']]);
                    
                    // Update session
                    $_SESSION['nama_lengkap'] = $nama_lengkap;
                    $_SESSION['email'] = $email;
                    
                    setAlert('success', 'Profil berhasil diupdate');
                    break;
                    
                case 'change_password':
                    $oldPassword = $_POST['old_password'];
                    $newPassword = $_POST['new_password'];
                    $confirmPassword = $_POST['confirm_password'];
                    
                    if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
                        throw new Exception('Semua field password harus diisi');
                    }
                    
                    if (strlen($newPassword) < 6) {
                        throw new Exception('Password baru minimal 6 karakter');
                    }
                    
                    if ($newPassword !== $confirmPassword) {
                        throw new Exception('Konfirmasi password baru tidak cocok');
                    }
                    
                    $result = Auth::updatePassword($_SESSION['user_id'], $oldPassword, $newPassword);
                    
                    if ($result['success']) {
                        setAlert('success', $result['message']);
                    } else {
                        setAlert('danger', $result['message']);
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('profile.php');
}

// Ambil data user
$user = Auth::getUserInfo($_SESSION['user_id']);

include 'includes/header.php';
?>

<div class="row">
    <!-- Informasi Profil -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Informasi Profil
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h5><?php echo $user['nama_lengkap']; ?></h5>
                <p class="text-muted"><?php echo $user['email']; ?></p>
                <span class="badge bg-<?php echo $user['role'] == 'admin' ? 'danger' : 'success'; ?> fs-6">
                    <?php echo ucfirst($user['role']); ?>
                </span>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <strong>Username</strong>
                        <p class="text-muted"><?php echo $user['username']; ?></p>
                    </div>
                    <div class="col-6">
                        <strong>Bergabung</strong>
                        <p class="text-muted"><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Edit Profil -->
    <div class="col-lg-8">
        <div class="row">
            <!-- Update Profil -->
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>Edit Profil
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" 
                                               value="<?php echo $user['username']; ?>" disabled>
                                        <div class="form-text">Username tidak dapat diubah</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="role" class="form-label">Role</label>
                                        <input type="text" class="form-control" id="role" 
                                               value="<?php echo ucfirst($user['role']); ?>" disabled>
                                        <div class="form-text">Role hanya dapat diubah oleh admin</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="nama_lengkap" class="form-label">Nama Lengkap</label>
                                <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" 
                                       value="<?php echo $user['nama_lengkap']; ?>" required maxlength="100">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo $user['email']; ?>" required maxlength="100">
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Profil
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Ganti Password -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-key me-2"></i>Ganti Password
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="passwordForm">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="old_password" class="form-label">Password Lama</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="old_password" 
                                           name="old_password" required placeholder="Masukkan password lama">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="togglePassword('old_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">Password Baru</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="new_password" 
                                                   name="new_password" required minlength="6" 
                                                   placeholder="Minimal 6 karakter">
                                            <button class="btn btn-outline-secondary" type="button" 
                                                    onclick="togglePassword('new_password')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirm_password" 
                                                   name="confirm_password" required 
                                                   placeholder="Ulangi password baru">
                                            <button class="btn btn-outline-secondary" type="button" 
                                                    onclick="togglePassword('confirm_password')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key me-2"></i>Ganti Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$pageScripts = "
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Validasi konfirmasi password
document.getElementById('confirm_password').addEventListener('blur', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Password tidak cocok');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Validasi form password
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Konfirmasi password tidak cocok');
        return false;
    }
});
</script>
";

include 'includes/footer.php';
?>
