<?php
require_once 'config/config.php';

$pageTitle = 'Dashboard';

// Ambil statistik data
try {
    $stats = [
        'total_kriteria' => fetchOne("SELECT COUNT(*) as total FROM kriteria")['total'],
        'total_alternatif' => fetchOne("SELECT COUNT(*) as total FROM alternatif")['total'],
        'total_users' => fetchOne("SELECT COUNT(*) as total FROM users")['total'],
        'total_perbandingan' => fetchOne("SELECT COUNT(*) as total FROM perbandingan_kriteria")['total']
    ];
    
    // Ambil data untuk grafik
    $kriteriaData = fetchAll("SELECT nama_kriteria, bobot FROM kriteria ORDER BY bobot DESC");
    $alternatifData = fetchAll("
        SELECT a.nama_alternatif, COALESCE(AVG(na.nilai), 0) as rata_nilai
        FROM alternatif a
        LEFT JOIN nilai_alternatif na ON a.id = na.alternatif_id
        GROUP BY a.id, a.nama_alternatif
        ORDER BY rata_nilai DESC
    ");
    
    // Ambil ranking terbaru
    $latestRanking = fetchAll("
        SELECT a.nama_alternatif, ht.skor_preferensi, ht.ranking
        FROM hasil_topsis ht
        JOIN alternatif a ON ht.alternatif_id = a.id
        WHERE ht.tanggal_perhitungan = (
            SELECT MAX(tanggal_perhitungan) FROM hasil_topsis
        )
        ORDER BY ht.ranking ASC
        LIMIT 5
    ");
    
} catch (Exception $e) {
    writeLog("Dashboard error: " . $e->getMessage(), 'ERROR');
    $stats = ['total_kriteria' => 0, 'total_alternatif' => 0, 'total_users' => 0, 'total_perbandingan' => 0];
    $kriteriaData = [];
    $alternatifData = [];
    $latestRanking = [];
}

include 'includes/header.php';
?>

<!-- Dashboard Content -->
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Kriteria
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_kriteria']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="stats-icon">
                            <i class="fas fa-list-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Alternatif
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_alternatif']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="stats-icon bg-success">
                            <i class="fas fa-leaf"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isAdmin()): ?>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_users']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="stats-icon bg-info">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Perbandingan AHP
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_perbandingan']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="stats-icon bg-warning">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Grafik Bobot Kriteria -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i>
                Bobot Kriteria (AHP)
            </div>
            <div class="card-body">
                <canvas id="kriteriaChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Grafik Rata-rata Nilai Alternatif -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                Rata-rata Nilai Alternatif
            </div>
            <div class="card-body">
                <canvas id="alternatifChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Ranking Terbaru -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy me-2"></i>
                Ranking Terbaru (TOPSIS)
            </div>
            <div class="card-body">
                <?php if (!empty($latestRanking)): ?>
                    <?php foreach ($latestRanking as $index => $item): ?>
                        <div class="ranking-item">
                            <div class="d-flex align-items-center">
                                <div class="ranking-badge me-3">
                                    <?php echo $item['ranking']; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $item['nama_alternatif']; ?></h6>
                                    <small class="text-muted">
                                        Skor Preferensi: <?php echo formatNumber($item['skor_preferensi']); ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <?php if ($item['ranking'] == 1): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-crown me-1"></i>Terbaik
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="text-center mt-3">
                        <a href="ranking.php" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>Lihat Semua Ranking
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Belum ada data ranking. Silakan lakukan perhitungan TOPSIS terlebih dahulu.</p>
                        <a href="ranking.php" class="btn btn-primary">
                            <i class="fas fa-calculator me-2"></i>Hitung Ranking
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                Aksi Cepat
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="kriteria.php" class="btn btn-outline-primary">
                        <i class="fas fa-list-alt me-2"></i>Kelola Kriteria
                    </a>
                    <a href="alternatif.php" class="btn btn-outline-success">
                        <i class="fas fa-leaf me-2"></i>Kelola Alternatif
                    </a>
                    <a href="nilai_alternatif.php" class="btn btn-outline-info">
                        <i class="fas fa-calculator me-2"></i>Input Nilai
                    </a>
                    <a href="perbandingan.php" class="btn btn-outline-warning">
                        <i class="fas fa-balance-scale me-2"></i>Perbandingan AHP
                    </a>
                    <a href="ranking.php" class="btn btn-outline-danger">
                        <i class="fas fa-trophy me-2"></i>Hitung Ranking
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$pageScripts = "
<script>
// Chart untuk Bobot Kriteria
const kriteriaCtx = document.getElementById('kriteriaChart').getContext('2d');
const kriteriaChart = new Chart(kriteriaCtx, {
    type: 'doughnut',
    data: {
        labels: " . json_encode(array_column($kriteriaData, 'nama_kriteria')) . ",
        datasets: [{
            data: " . json_encode(array_column($kriteriaData, 'bobot')) . ",
            backgroundColor: [
                '#2E8B57',
                '#228B22',
                '#32CD32',
                '#90EE90',
                '#98FB98',
                '#F0FFF0'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Chart untuk Rata-rata Nilai Alternatif
const alternatifCtx = document.getElementById('alternatifChart').getContext('2d');
const alternatifChart = new Chart(alternatifCtx, {
    type: 'bar',
    data: {
        labels: " . json_encode(array_column($alternatifData, 'nama_alternatif')) . ",
        datasets: [{
            label: 'Rata-rata Nilai',
            data: " . json_encode(array_column($alternatifData, 'rata_nilai')) . ",
            backgroundColor: 'rgba(46, 139, 87, 0.8)',
            borderColor: 'rgba(46, 139, 87, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
";

include 'includes/footer.php';
?>
