<?php
/**
 * Class untuk menangani autentikasi user
 */

require_once '../config/config.php';

class Auth {
    
    /**
     * Login user
     */
    public static function login($username, $password) {
        try {
            $query = "SELECT id, username, password, nama_lengkap, email, role 
                     FROM users 
                     WHERE username = ? OR email = ?";
            
            $user = fetchOne($query, [$username, $username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                
                // Log login
                writeLog("User {$user['username']} logged in successfully");
                
                return [
                    'success' => true,
                    'message' => 'Login berhasil',
                    'user' => $user
                ];
            } else {
                writeLog("Failed login attempt for username: $username");
                return [
                    'success' => false,
                    'message' => 'Username/email atau password salah'
                ];
            }
        } catch (Exception $e) {
            writeLog("Login error: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan sistem'
            ];
        }
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        $username = $_SESSION['username'] ?? 'Unknown';
        
        // Destroy session
        session_destroy();
        
        // Log logout
        writeLog("User $username logged out");
        
        return [
            'success' => true,
            'message' => 'Logout berhasil'
        ];
    }
    
    /**
     * Register user baru
     */
    public static function register($data) {
        try {
            // Validasi input
            $errors = self::validateRegistration($data);
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => implode(', ', $errors)
                ];
            }
            
            // Cek apakah username atau email sudah ada
            $checkQuery = "SELECT id FROM users WHERE username = ? OR email = ?";
            $existing = fetchOne($checkQuery, [$data['username'], $data['email']]);
            
            if ($existing) {
                return [
                    'success' => false,
                    'message' => 'Username atau email sudah terdaftar'
                ];
            }
            
            // Hash password
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Insert user baru
            $insertQuery = "INSERT INTO users (username, password, nama_lengkap, email, role) 
                           VALUES (?, ?, ?, ?, ?)";
            
            $userId = insertData($insertQuery, [
                $data['username'],
                $hashedPassword,
                $data['nama_lengkap'],
                $data['email'],
                $data['role'] ?? 'user'
            ]);
            
            writeLog("New user registered: {$data['username']}");
            
            return [
                'success' => true,
                'message' => 'Registrasi berhasil',
                'user_id' => $userId
            ];
            
        } catch (Exception $e) {
            writeLog("Registration error: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan sistem'
            ];
        }
    }
    
    /**
     * Validasi data registrasi
     */
    private static function validateRegistration($data) {
        $errors = [];
        
        if (empty($data['username']) || strlen($data['username']) < 3) {
            $errors[] = 'Username minimal 3 karakter';
        }
        
        if (empty($data['password']) || strlen($data['password']) < 6) {
            $errors[] = 'Password minimal 6 karakter';
        }
        
        if (empty($data['nama_lengkap'])) {
            $errors[] = 'Nama lengkap harus diisi';
        }
        
        if (empty($data['email']) || !isValidEmail($data['email'])) {
            $errors[] = 'Email tidak valid';
        }
        
        if (isset($data['confirm_password']) && $data['password'] !== $data['confirm_password']) {
            $errors[] = 'Konfirmasi password tidak cocok';
        }
        
        return $errors;
    }
    
    /**
     * Update password user
     */
    public static function updatePassword($userId, $oldPassword, $newPassword) {
        try {
            // Ambil password lama
            $user = fetchOne("SELECT password FROM users WHERE id = ?", [$userId]);
            
            if (!$user || !password_verify($oldPassword, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'Password lama salah'
                ];
            }
            
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $affected = executeUpdate(
                "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?",
                [$hashedPassword, $userId]
            );
            
            if ($affected > 0) {
                writeLog("Password updated for user ID: $userId");
                return [
                    'success' => true,
                    'message' => 'Password berhasil diupdate'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Gagal mengupdate password'
                ];
            }
            
        } catch (Exception $e) {
            writeLog("Update password error: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan sistem'
            ];
        }
    }
    
    /**
     * Get user info
     */
    public static function getUserInfo($userId) {
        try {
            $query = "SELECT id, username, nama_lengkap, email, role, created_at 
                     FROM users WHERE id = ?";
            return fetchOne($query, [$userId]);
        } catch (Exception $e) {
            writeLog("Get user info error: " . $e->getMessage(), 'ERROR');
            return null;
        }
    }
}
?>
