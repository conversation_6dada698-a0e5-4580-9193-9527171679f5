<?php
require_once 'config/config.php';

$pageTitle = 'Nilai Alternatif';

// Proses form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'save_nilai':
                    $alternatif_id = (int)$_POST['alternatif_id'];
                    $kriteria_id = (int)$_POST['kriteria_id'];
                    $nilai = (float)$_POST['nilai'];
                    
                    if ($nilai < 0 || $nilai > 10) {
                        throw new Exception('<PERSON>lai harus antara 0 dan 10');
                    }
                    
                    // Cek apakah sudah ada nilai untuk kombinasi ini
                    $existing = fetchOne("SELECT id FROM nilai_alternatif WHERE alternatif_id = ? AND kriteria_id = ?", 
                                       [$alternatif_id, $kriteria_id]);
                    
                    if ($existing) {
                        // Update
                        $query = "UPDATE nilai_alternatif SET nilai = ? WHERE alternatif_id = ? AND kriteria_id = ?";
                        executeUpdate($query, [$nilai, $alternatif_id, $kriteria_id]);
                        setAlert('success', 'Nilai berhasil diupdate');
                    } else {
                        // Insert
                        $query = "INSERT INTO nilai_alternatif (alternatif_id, kriteria_id, nilai) VALUES (?, ?, ?)";
                        insertData($query, [$alternatif_id, $kriteria_id, $nilai]);
                        setAlert('success', 'Nilai berhasil disimpan');
                    }
                    break;
                    
                case 'delete_nilai':
                    $id = (int)$_POST['id'];
                    executeUpdate("DELETE FROM nilai_alternatif WHERE id = ?", [$id]);
                    setAlert('success', 'Nilai berhasil dihapus');
                    break;
                    
                case 'save_batch':
                    $conn = getDBConnection();
                    $conn->beginTransaction();
                    
                    try {
                        foreach ($_POST['nilai'] as $alternatif_id => $kriteria_values) {
                            foreach ($kriteria_values as $kriteria_id => $nilai) {
                                if ($nilai !== '' && is_numeric($nilai)) {
                                    $nilai = (float)$nilai;
                                    
                                    if ($nilai < 0 || $nilai > 10) {
                                        throw new Exception("Nilai untuk alternatif ID $alternatif_id, kriteria ID $kriteria_id harus antara 0 dan 10");
                                    }
                                    
                                    // Cek existing
                                    $existing = fetchOne("SELECT id FROM nilai_alternatif WHERE alternatif_id = ? AND kriteria_id = ?", 
                                                       [$alternatif_id, $kriteria_id]);
                                    
                                    if ($existing) {
                                        executeUpdate("UPDATE nilai_alternatif SET nilai = ? WHERE alternatif_id = ? AND kriteria_id = ?", 
                                                    [$nilai, $alternatif_id, $kriteria_id]);
                                    } else {
                                        insertData("INSERT INTO nilai_alternatif (alternatif_id, kriteria_id, nilai) VALUES (?, ?, ?)", 
                                                 [$alternatif_id, $kriteria_id, $nilai]);
                                    }
                                }
                            }
                        }
                        
                        $conn->commit();
                        setAlert('success', 'Semua nilai berhasil disimpan');
                    } catch (Exception $e) {
                        $conn->rollback();
                        throw $e;
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        setAlert('danger', $e->getMessage());
    }
    
    redirect('nilai_alternatif.php');
}

// Ambil data
$kriteria = fetchAll("SELECT * FROM kriteria ORDER BY kode_kriteria");
$alternatif = fetchAll("SELECT * FROM alternatif ORDER BY kode_alternatif");

// Ambil nilai yang sudah ada
$nilai_existing = [];
$nilai_data = fetchAll("
    SELECT na.*, k.nama_kriteria, a.nama_alternatif 
    FROM nilai_alternatif na
    JOIN kriteria k ON na.kriteria_id = k.id
    JOIN alternatif a ON na.alternatif_id = a.id
");

foreach ($nilai_data as $item) {
    $nilai_existing[$item['alternatif_id']][$item['kriteria_id']] = $item['nilai'];
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Form Input Batch -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>Input Nilai Alternatif
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($kriteria) || empty($alternatif)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Pastikan sudah ada data kriteria dan alternatif sebelum input nilai.
                        <div class="mt-2">
                            <?php if (empty($kriteria)): ?>
                                <a href="kriteria.php" class="btn btn-sm btn-primary me-2">
                                    <i class="fas fa-plus me-1"></i>Tambah Kriteria
                                </a>
                            <?php endif; ?>
                            <?php if (empty($alternatif)): ?>
                                <a href="alternatif.php" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus me-1"></i>Tambah Alternatif
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <form method="POST" id="batchForm">
                        <input type="hidden" name="action" value="save_batch">
                        
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Alternatif</th>
                                        <?php foreach ($kriteria as $k): ?>
                                            <th class="text-center">
                                                <?php echo $k['kode_kriteria']; ?><br>
                                                <small><?php echo $k['nama_kriteria']; ?></small>
                                            </th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($alternatif as $alt): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $alt['kode_alternatif']; ?></strong><br>
                                                <small class="text-muted"><?php echo $alt['nama_alternatif']; ?></small>
                                            </td>
                                            <?php foreach ($kriteria as $k): ?>
                                                <td>
                                                    <input type="number" 
                                                           class="form-control form-control-sm text-center" 
                                                           name="nilai[<?php echo $alt['id']; ?>][<?php echo $k['id']; ?>]"
                                                           value="<?php echo isset($nilai_existing[$alt['id']][$k['id']]) ? formatNumber($nilai_existing[$alt['id']][$k['id']], 2) : ''; ?>"
                                                           min="0" max="10" step="0.01"
                                                           placeholder="0-10">
                                                </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Semua Nilai
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Petunjuk:</strong> Masukkan nilai antara 0-10 untuk setiap kombinasi alternatif dan kriteria. 
                            Nilai yang kosong akan diabaikan.
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Tabel Data Nilai -->
    <?php if (!empty($nilai_data)): ?>
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>Data Nilai yang Tersimpan
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Alternatif</th>
                                <th>Kriteria</th>
                                <th>Nilai</th>
                                <th>Tanggal Input</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($nilai_data as $index => $item): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <strong><?php echo $item['nama_alternatif']; ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $item['nama_kriteria']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success fs-6"><?php echo formatNumber($item['nilai'], 2); ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($item['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="deleteNilai(<?php echo $item['id']; ?>, '<?php echo $item['nama_alternatif']; ?>', '<?php echo $item['nama_kriteria']; ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
$pageScripts = "
<script>
function deleteNilai(id, alternatif, kriteria) {
    if (confirm('Yakin ingin menghapus nilai untuk \"' + alternatif + '\" pada kriteria \"' + kriteria + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete_nilai\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

// Validasi input nilai
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type=\"number\"]');
    inputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validateNumber(this, 0, 10);
        });
    });
});
</script>
";

include 'includes/footer.php';
?>
