# Perubahan Nama Database

## <PERSON><PERSON><PERSON>bahan

Nama database telah diubah dari `spk_pupuk_organik` men<PERSON><PERSON> `spk_amingflora` untuk lebih mencerminkan identitas Aming Flora.

## File yang Telah Diperbarui

### 1. Database Structure
- ✅ `database.sql` - Struktur database utama
- ✅ `database_amingflora.sql` - File database baru dengan nama yang sesuai

### 2. Konfigurasi
- ✅ `config/database.php` - Konfigurasi koneksi database

### 3. Dokumentasi
- ✅ `README.md` - Dokumentasi utama
- ✅ `INSTALL.md` - Panduan instalasi

## Langkah Instalasi Terbaru

### 1. Buat Database Baru
```sql
CREATE DATABASE spk_amingflora;
```

### 2. Import Database
```bash
mysql -u root -p spk_amingflora < database.sql
```
atau
```bash
mysql -u root -p spk_amingflora < database_amingflora.sql
```

### 3. Konfigurasi Database
Edit `config/database.php`:
```php
private $db_name = 'spk_amingflora';
```

## Data Default yang Tersedia

### Admin Default
- **Username**: `admin`
- **Password**: `password`
- **Email**: `<EMAIL>`
- **Nama**: `Administrator Aming Flora`

### Kriteria (6 kriteria)
1. **C1 - Kebutuhan Nutrisi** (Benefit)
2. **C2 - Kondisi Tanah** (Benefit)
3. **C3 - Daya Serap** (Benefit)
4. **C4 - Biaya** (Cost)
5. **C5 - Ketersediaan** (Benefit)
6. **C6 - Dampak Lingkungan** (Benefit)

### Alternatif (4 jenis pupuk)
1. **A1 - Kompos Bokashi**
2. **A2 - Pupuk Kandang**
3. **A3 - Pupuk Organik Cair**
4. **A4 - Pupuk Organik Padat Komersial**

## Catatan Penting

- Semua deskripsi data telah disesuaikan dengan konteks Aming Flora
- Struktur tabel dan relasi tetap sama
- Tidak ada perubahan pada logika aplikasi
- Hanya nama database dan beberapa deskripsi yang diperbarui

## Verifikasi Instalasi

Setelah instalasi, pastikan:
1. ✅ Database `spk_amingflora` berhasil dibuat
2. ✅ Semua tabel berhasil diimport (8 tabel)
3. ✅ Data default tersedia (1 admin, 6 kriteria, 4 alternatif)
4. ✅ Aplikasi dapat diakses dan login berhasil
5. ✅ Semua fitur berfungsi normal

## Troubleshooting

### Jika masih menggunakan nama database lama:
1. Hapus database lama: `DROP DATABASE spk_pupuk_organik;`
2. Buat database baru: `CREATE DATABASE spk_amingflora;`
3. Import ulang dengan file terbaru

### Jika error koneksi:
1. Periksa konfigurasi di `config/database.php`
2. Pastikan nama database sudah `spk_amingflora`
3. Restart web server jika diperlukan

---

**Database siap digunakan untuk Aming Flora! 🌱**
